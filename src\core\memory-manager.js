/**
 * Memory Manager - Persistent memory and context management with local database storage
 */

import fs from 'fs/promises';
import path from 'path';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';

export class MemoryManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger.child('MemoryManager');
        
        this.dbPath = path.join(process.cwd(), 'data', 'memory.json');
        this.sessionsPath = path.join(process.cwd(), 'data', 'sessions');
        this.contextPath = path.join(process.cwd(), 'data', 'context');
        
        this.memory = {
            conversations: new Map(),
            sessions: new Map(),
            longTermMemory: new Map(),
            contextHistory: new Map(),
            userPreferences: new Map(),
            projectMemory: new Map()
        };
        
        this.currentSession = null;
        this.maxMemorySize = 10000; // Maximum number of entries
        this.maxSessionAge = 7 * 24 * 60 * 60 * 1000; // 7 days
        this.compressionThreshold = 1000; // Compress after this many entries
        
        this.isInitialized = false;
    }
    
    async initialize() {
        try {
            this.logger.info('Initializing Memory Manager');
            
            // Create data directories
            await this.ensureDirectories();
            
            // Load existing memory
            await this.loadMemory();
            
            // Start cleanup scheduler
            this.startCleanupScheduler();
            
            this.isInitialized = true;
            this.logger.info('Memory Manager initialized successfully');
            
            return true;
        } catch (error) {
            this.logger.error('Failed to initialize Memory Manager:', error);
            throw error;
        }
    }
    
    async ensureDirectories() {
        const dirs = [
            path.dirname(this.dbPath),
            this.sessionsPath,
            this.contextPath
        ];
        
        for (const dir of dirs) {
            try {
                await fs.mkdir(dir, { recursive: true });
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw error;
                }
            }
        }
    }
    
    async loadMemory() {
        try {
            // Load main memory database
            const data = await fs.readFile(this.dbPath, 'utf8');
            const parsed = JSON.parse(data);
            
            // Convert arrays back to Maps
            this.memory.conversations = new Map(parsed.conversations || []);
            this.memory.sessions = new Map(parsed.sessions || []);
            this.memory.longTermMemory = new Map(parsed.longTermMemory || []);
            this.memory.contextHistory = new Map(parsed.contextHistory || []);
            this.memory.userPreferences = new Map(parsed.userPreferences || []);
            this.memory.projectMemory = new Map(parsed.projectMemory || []);
            
            this.logger.info('Memory loaded successfully', {
                conversations: this.memory.conversations.size,
                sessions: this.memory.sessions.size,
                longTermMemory: this.memory.longTermMemory.size
            });
            
        } catch (error) {
            if (error.code === 'ENOENT') {
                this.logger.info('No existing memory database found, starting fresh');
            } else {
                this.logger.warn('Failed to load memory database:', error);
            }
        }
    }
    
    async saveMemory() {
        try {
            // Convert Maps to arrays for JSON serialization
            const data = {
                conversations: Array.from(this.memory.conversations.entries()),
                sessions: Array.from(this.memory.sessions.entries()),
                longTermMemory: Array.from(this.memory.longTermMemory.entries()),
                contextHistory: Array.from(this.memory.contextHistory.entries()),
                userPreferences: Array.from(this.memory.userPreferences.entries()),
                projectMemory: Array.from(this.memory.projectMemory.entries()),
                lastSaved: Date.now()
            };
            
            await fs.writeFile(this.dbPath, JSON.stringify(data, null, 2));
            this.logger.debug('Memory saved successfully');
            
        } catch (error) {
            this.logger.error('Failed to save memory:', error);
            throw error;
        }
    }
    
    async createSession(userId = 'default', metadata = {}) {
        const sessionId = this.generateSessionId();
        const session = {
            id: sessionId,
            userId,
            createdAt: Date.now(),
            lastAccessedAt: Date.now(),
            metadata,
            conversationHistory: [],
            context: new Map(),
            preferences: new Map()
        };
        
        this.memory.sessions.set(sessionId, session);
        this.currentSession = session;
        
        // Save session to file
        await this.saveSession(session);
        
        this.logger.info(`Created new session: ${sessionId}`);
        this.emit('sessionCreated', session);
        
        return sessionId;
    }
    
    async loadSession(sessionId) {
        try {
            // Try to load from memory first
            let session = this.memory.sessions.get(sessionId);
            
            if (!session) {
                // Try to load from file
                const sessionFile = path.join(this.sessionsPath, `${sessionId}.json`);
                const data = await fs.readFile(sessionFile, 'utf8');
                session = JSON.parse(data);
                
                // Convert context back to Map
                session.context = new Map(session.context || []);
                session.preferences = new Map(session.preferences || []);
                
                this.memory.sessions.set(sessionId, session);
            }
            
            session.lastAccessedAt = Date.now();
            this.currentSession = session;
            
            this.logger.info(`Loaded session: ${sessionId}`);
            this.emit('sessionLoaded', session);
            
            return session;
            
        } catch (error) {
            this.logger.error(`Failed to load session ${sessionId}:`, error);
            throw error;
        }
    }
    
    async saveSession(session) {
        try {
            const sessionFile = path.join(this.sessionsPath, `${session.id}.json`);
            const data = {
                ...session,
                context: Array.from(session.context.entries()),
                preferences: Array.from(session.preferences.entries())
            };
            
            await fs.writeFile(sessionFile, JSON.stringify(data, null, 2));
            
        } catch (error) {
            this.logger.error(`Failed to save session ${session.id}:`, error);
            throw error;
        }
    }
    
    async addConversationEntry(query, response, metadata = {}) {
        if (!this.currentSession) {
            await this.createSession();
        }
        
        const entry = {
            id: this.generateEntryId(),
            timestamp: Date.now(),
            query,
            response,
            metadata,
            sessionId: this.currentSession.id
        };
        
        // Add to current session
        this.currentSession.conversationHistory.push(entry);
        
        // Add to global conversations
        this.memory.conversations.set(entry.id, entry);
        
        // Extract and store important information for long-term memory
        await this.extractLongTermMemory(entry);
        
        // Save session
        await this.saveSession(this.currentSession);
        
        this.emit('conversationAdded', entry);
        
        return entry.id;
    }
    
    async extractLongTermMemory(entry) {
        try {
            // Extract key information that should be remembered long-term
            const keyPhrases = this.extractKeyPhrases(entry.query + ' ' + entry.response);
            const concepts = this.extractConcepts(entry);
            const preferences = this.extractPreferences(entry);
            
            // Store key phrases
            for (const phrase of keyPhrases) {
                const hash = this.hashString(phrase);
                const existing = this.memory.longTermMemory.get(hash) || {
                    phrase,
                    count: 0,
                    firstSeen: Date.now(),
                    lastSeen: Date.now(),
                    contexts: []
                };
                
                existing.count++;
                existing.lastSeen = Date.now();
                existing.contexts.push({
                    sessionId: entry.sessionId,
                    entryId: entry.id,
                    timestamp: entry.timestamp
                });
                
                // Keep only recent contexts
                existing.contexts = existing.contexts.slice(-10);
                
                this.memory.longTermMemory.set(hash, existing);
            }
            
            // Store concepts
            for (const concept of concepts) {
                const hash = this.hashString(concept.name);
                this.memory.longTermMemory.set(`concept_${hash}`, {
                    type: 'concept',
                    name: concept.name,
                    category: concept.category,
                    confidence: concept.confidence,
                    lastSeen: Date.now(),
                    relatedEntries: [entry.id]
                });
            }
            
            // Store preferences
            for (const [key, value] of Object.entries(preferences)) {
                this.memory.userPreferences.set(key, {
                    value,
                    confidence: value.confidence || 0.5,
                    lastUpdated: Date.now(),
                    source: entry.id
                });
            }
            
        } catch (error) {
            this.logger.warn('Failed to extract long-term memory:', error);
        }
    }
    
    extractKeyPhrases(text) {
        // Simple key phrase extraction
        const phrases = [];
        const words = text.toLowerCase().match(/\b\w+\b/g) || [];
        
        // Extract programming-related terms
        const programmingTerms = [
            'javascript', 'python', 'react', 'node', 'api', 'database',
            'function', 'class', 'component', 'service', 'module',
            'error', 'bug', 'fix', 'optimize', 'performance',
            'security', 'authentication', 'authorization', 'deployment'
        ];
        
        for (const term of programmingTerms) {
            if (words.includes(term)) {
                phrases.push(term);
            }
        }
        
        // Extract multi-word phrases
        const multiWordPatterns = [
            /\b(machine learning|artificial intelligence|deep learning)\b/gi,
            /\b(web development|mobile development|full stack)\b/gi,
            /\b(unit test|integration test|end to end)\b/gi,
            /\b(code review|pull request|merge request)\b/gi
        ];
        
        for (const pattern of multiWordPatterns) {
            const matches = text.match(pattern);
            if (matches) {
                phrases.push(...matches.map(m => m.toLowerCase()));
            }
        }
        
        return [...new Set(phrases)]; // Remove duplicates
    }
    
    extractConcepts(entry) {
        const concepts = [];
        const text = entry.query + ' ' + entry.response;
        
        // Technology concepts
        const techPatterns = {
            'framework': /\b(react|vue|angular|express|django|flask|spring)\b/gi,
            'language': /\b(javascript|python|java|c\+\+|rust|go|php)\b/gi,
            'database': /\b(mysql|postgresql|mongodb|redis|sqlite)\b/gi,
            'cloud': /\b(aws|azure|gcp|docker|kubernetes)\b/gi
        };
        
        for (const [category, pattern] of Object.entries(techPatterns)) {
            const matches = text.match(pattern);
            if (matches) {
                for (const match of matches) {
                    concepts.push({
                        name: match.toLowerCase(),
                        category,
                        confidence: 0.8
                    });
                }
            }
        }
        
        return concepts;
    }
    
    extractPreferences(entry) {
        const preferences = {};
        const text = entry.query + ' ' + entry.response;
        
        // Extract language preferences
        if (text.includes('prefer') || text.includes('like')) {
            const langMatch = text.match(/prefer\s+(\w+)|like\s+(\w+)/i);
            if (langMatch) {
                preferences.preferredLanguage = {
                    value: langMatch[1] || langMatch[2],
                    confidence: 0.7
                };
            }
        }
        
        // Extract coding style preferences
        if (text.includes('tabs') || text.includes('spaces')) {
            preferences.indentationStyle = {
                value: text.includes('tabs') ? 'tabs' : 'spaces',
                confidence: 0.6
            };
        }
        
        return preferences;
    }
    
    async getRelevantMemory(query, limit = 10) {
        const relevantEntries = [];
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.match(/\b\w+\b/g) || [];
        
        // Search conversations
        for (const [id, entry] of this.memory.conversations) {
            const score = this.calculateRelevanceScore(entry, queryWords);
            if (score > 0.3) {
                relevantEntries.push({
                    ...entry,
                    relevanceScore: score,
                    type: 'conversation'
                });
            }
        }
        
        // Search long-term memory
        for (const [id, memory] of this.memory.longTermMemory) {
            if (memory.phrase) {
                const score = this.calculateMemoryRelevanceScore(memory, queryWords);
                if (score > 0.4) {
                    relevantEntries.push({
                        ...memory,
                        relevanceScore: score,
                        type: 'longTerm'
                    });
                }
            }
        }
        
        // Sort by relevance and recency
        relevantEntries.sort((a, b) => {
            const scoreA = a.relevanceScore * 0.7 + (a.lastSeen || a.timestamp) / Date.now() * 0.3;
            const scoreB = b.relevanceScore * 0.7 + (b.lastSeen || b.timestamp) / Date.now() * 0.3;
            return scoreB - scoreA;
        });
        
        return relevantEntries.slice(0, limit);
    }
    
    calculateRelevanceScore(entry, queryWords) {
        const text = (entry.query + ' ' + entry.response).toLowerCase();
        let score = 0;
        
        for (const word of queryWords) {
            if (text.includes(word)) {
                score += 1;
            }
        }
        
        return score / queryWords.length;
    }
    
    calculateMemoryRelevanceScore(memory, queryWords) {
        const text = memory.phrase.toLowerCase();
        let score = 0;
        
        for (const word of queryWords) {
            if (text.includes(word)) {
                score += 1;
            }
        }
        
        // Boost score based on frequency
        score *= Math.log(memory.count + 1);
        
        return score / queryWords.length;
    }
    
    async getSessionHistory(sessionId, limit = 50) {
        const session = await this.loadSession(sessionId);
        return session.conversationHistory.slice(-limit);
    }
    
    async getUserPreferences(userId = 'default') {
        const preferences = {};
        
        for (const [key, value] of this.memory.userPreferences) {
            preferences[key] = value.value;
        }
        
        return preferences;
    }
    
    async setUserPreference(key, value, userId = 'default') {
        this.memory.userPreferences.set(key, {
            value,
            confidence: 1.0,
            lastUpdated: Date.now(),
            userId
        });
        
        await this.saveMemory();
    }
    
    async addProjectMemory(projectPath, key, value) {
        const projectHash = this.hashString(projectPath);
        const projectMemory = this.memory.projectMemory.get(projectHash) || {
            path: projectPath,
            memories: new Map()
        };
        
        projectMemory.memories.set(key, {
            value,
            timestamp: Date.now()
        });
        
        this.memory.projectMemory.set(projectHash, projectMemory);
        await this.saveMemory();
    }
    
    async getProjectMemory(projectPath, key = null) {
        const projectHash = this.hashString(projectPath);
        const projectMemory = this.memory.projectMemory.get(projectHash);
        
        if (!projectMemory) return null;
        
        if (key) {
            return projectMemory.memories.get(key);
        }
        
        return Object.fromEntries(projectMemory.memories.entries());
    }
    
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    generateEntryId() {
        return `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    hashString(str) {
        return createHash('md5').update(str).digest('hex');
    }
    
    startCleanupScheduler() {
        // Run cleanup every hour
        setInterval(() => {
            this.performCleanup();
        }, 60 * 60 * 1000);
    }
    
    async performCleanup() {
        try {
            this.logger.info('Performing memory cleanup');
            
            const now = Date.now();
            let cleaned = 0;
            
            // Clean old sessions
            for (const [sessionId, session] of this.memory.sessions) {
                if (now - session.lastAccessedAt > this.maxSessionAge) {
                    this.memory.sessions.delete(sessionId);
                    
                    // Delete session file
                    try {
                        await fs.unlink(path.join(this.sessionsPath, `${sessionId}.json`));
                    } catch (error) {
                        // Ignore file not found errors
                    }
                    
                    cleaned++;
                }
            }
            
            // Clean old conversations if memory is too large
            if (this.memory.conversations.size > this.maxMemorySize) {
                const entries = Array.from(this.memory.conversations.entries())
                    .sort((a, b) => b[1].timestamp - a[1].timestamp);
                
                const toKeep = entries.slice(0, this.maxMemorySize * 0.8);
                this.memory.conversations = new Map(toKeep);
                
                cleaned += entries.length - toKeep.length;
            }
            
            // Save cleaned memory
            await this.saveMemory();
            
            this.logger.info(`Cleanup completed, removed ${cleaned} entries`);
            
        } catch (error) {
            this.logger.error('Memory cleanup failed:', error);
        }
    }
    
    async getMemoryStats() {
        return {
            conversations: this.memory.conversations.size,
            sessions: this.memory.sessions.size,
            longTermMemory: this.memory.longTermMemory.size,
            contextHistory: this.memory.contextHistory.size,
            userPreferences: this.memory.userPreferences.size,
            projectMemory: this.memory.projectMemory.size,
            currentSession: this.currentSession?.id || null,
            isInitialized: this.isInitialized
        };
    }
    
    async shutdown() {
        try {
            this.logger.info('Shutting down Memory Manager');
            
            // Save current state
            await this.saveMemory();
            
            // Save current session
            if (this.currentSession) {
                await this.saveSession(this.currentSession);
            }
            
            this.logger.info('Memory Manager shutdown complete');
            
        } catch (error) {
            this.logger.error('Memory Manager shutdown failed:', error);
            throw error;
        }
    }
}
