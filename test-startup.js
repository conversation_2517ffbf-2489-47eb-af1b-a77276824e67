#!/usr/bin/env node

/**
 * SHAI AI Assistant - Startup Test
 * Tests that the application can start without errors
 */

import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

console.log('🧪 Testing SHAI AI Assistant startup...\n');

async function testStartup() {
    try {
        console.log('1. Testing imports...');
        
        // Test core imports
        const { Logger } = await import('./src/utils/logger.js');
        console.log('   ✅ Logger imported successfully');
        
        const { ConfigManager } = await import('./src/utils/config.js');
        console.log('   ✅ ConfigManager imported successfully');
        
        const { GeminiClient } = await import('./src/core/gemini-client.js');
        console.log('   ✅ GeminiClient imported successfully');
        
        const { ContextEngine } = await import('./src/core/context-engine.js');
        console.log('   ✅ ContextEngine imported successfully');
        
        const { Orchestrator } = await import('./src/core/orchestrator.js');
        console.log('   ✅ Orchestrator imported successfully');
        
        console.log('\n2. Testing component initialization...');
        
        // Test Logger
        const logger = new Logger({
            level: 'error',
            enableConsole: false,
            enableFile: false
        });
        console.log('   ✅ Logger initialized successfully');
        
        // Test ConfigManager
        const config = new ConfigManager();
        await config.initialize();
        console.log('   ✅ ConfigManager initialized successfully');
        
        // Test ContextEngine (without actual file operations)
        const contextEngine = new ContextEngine(config, logger);
        console.log('   ✅ ContextEngine created successfully');
        
        // Test GeminiClient (without API calls)
        const geminiClient = new GeminiClient(config, logger);
        console.log('   ✅ GeminiClient created successfully');
        
        console.log('\n3. Testing agent imports...');
        
        // Test agent imports
        const { BaseAgent } = await import('./src/agents/base-agent.js');
        console.log('   ✅ BaseAgent imported successfully');
        
        const { CodeAnalysisAgent } = await import('./src/agents/code-analysis-agent.js');
        console.log('   ✅ CodeAnalysisAgent imported successfully');
        
        const { CodeGenerationAgent } = await import('./src/agents/code-generation-agent.js');
        console.log('   ✅ CodeGenerationAgent imported successfully');
        
        const { TestingAgent } = await import('./src/agents/testing-agent.js');
        console.log('   ✅ TestingAgent imported successfully');
        
        const { DocumentationAgent } = await import('./src/agents/documentation-agent.js');
        console.log('   ✅ DocumentationAgent imported successfully');
        
        const { DebuggingAgent } = await import('./src/agents/debugging-agent.js');
        console.log('   ✅ DebuggingAgent imported successfully');
        
        const { SecurityAgent } = await import('./src/agents/security-agent.js');
        console.log('   ✅ SecurityAgent imported successfully');
        
        console.log('\n4. Testing tool imports...');
        
        // Test tool imports
        const { FileSystemTools } = await import('./src/tools/file-system.js');
        console.log('   ✅ FileSystemTools imported successfully');
        
        const { GitOperations } = await import('./src/tools/git-operations.js');
        console.log('   ✅ GitOperations imported successfully');
        
        const { PackageManager } = await import('./src/tools/package-manager.js');
        console.log('   ✅ PackageManager imported successfully');
        
        const { TerminalTools } = await import('./src/tools/terminal.js');
        console.log('   ✅ TerminalTools imported successfully');
        
        console.log('\n5. Testing UI imports...');
        
        // Test UI imports
        const { CLIInterface } = await import('./src/ui/cli/index.js');
        console.log('   ✅ CLIInterface imported successfully');
        
        const { WebServer } = await import('./src/ui/web/server.js');
        console.log('   ✅ WebServer imported successfully');
        
        console.log('\n6. Testing Babel parser functionality...');
        
        // Test Babel parser with sample code
        const { parse } = await import('@babel/parser');
        const sampleCode = `
            import React from 'react';
            
            export default function HelloWorld() {
                const message = 'Hello, World!';
                return <div>{message}</div>;
            }
        `;
        
        const ast = parse(sampleCode, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript']
        });
        
        console.log('   ✅ Babel parser working correctly');
        console.log(`   📊 AST generated with ${ast.body ? ast.body.length : 0} top-level statements`);
        
        console.log('\n🎉 All tests passed! SHAI AI Assistant is ready to start.');
        console.log('\n📋 Next steps:');
        console.log('   1. Make sure you have a valid Gemini API key in your .env file');
        console.log('   2. Run: bun install (to install dependencies)');
        console.log('   3. Run: bun start (to start the application)');
        console.log('   4. Or run: bun run web (for web interface)');
        
        return true;
        
    } catch (error) {
        console.error('\n❌ Startup test failed:', error.message);
        console.error('\n🔍 Error details:', error);
        
        if (error.message.includes('tree-sitter')) {
            console.error('\n💡 This appears to be a tree-sitter related error.');
            console.error('   The context-engine.js file may still have tree-sitter imports.');
            console.error('   Please check that all tree-sitter references have been removed.');
        }
        
        if (error.message.includes('Cannot resolve module')) {
            console.error('\n💡 This appears to be a missing dependency.');
            console.error('   Please run: bun install');
        }
        
        return false;
    }
}

// Run the test
testStartup().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
});
