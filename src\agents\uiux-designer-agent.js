/**
 * UI/UX Designer Agent - Expert in design systems, wireframes, prototypes, and accessibility
 */

import { BaseAgent } from './base-agent.js';

export class UIUXDesignerAgent extends BaseAgent {
    constructor(orchestrator, logger) {
        super(orchestrator, logger);
        this.name = 'UIUXDesignerAgent';
        this.specialization = 'UI/UX Design';
        this.capabilities = [
            'User Experience (UX) Design',
            'User Interface (UI) Design',
            'Design Systems & Style Guides',
            'Wireframing & Prototyping',
            'User Research & Testing',
            'Accessibility Design (WCAG)',
            'Information Architecture',
            'Interaction Design',
            'Visual Design & Typography',
            'Color Theory & Branding',
            'Responsive Design Principles',
            'Design Tool Integration'
        ];
        
        this.designPrinciples = {
            ux: ['User-Centered Design', 'Design Thinking', 'Lean UX', 'Agile UX'],
            ui: ['Visual Hierarchy', 'Consistency', 'Feedback', 'Affordances'],
            accessibility: ['WCAG 2.1', 'Inclusive Design', 'Universal Design'],
            usability: ['Nielsen\'s Heuristics', '<PERSON>tts\' Law', 'Miller\'s Rule']
        };
        
        this.designTools = {
            wireframing: ['Figma', 'Sketch', 'Adobe XD', 'Balsamiq', 'Whimsical'],
            prototyping: ['Figma', 'Principle', 'Framer', 'InVision', 'Marvel'],
            userTesting: ['Maze', 'UserTesting', 'Hotjar', 'Optimal Workshop'],
            collaboration: ['Figma', 'Miro', 'FigJam', 'Abstract', 'Zeplin']
        };
        
        this.designSystems = {
            components: ['Buttons', 'Forms', 'Navigation', 'Cards', 'Modals'],
            tokens: ['Colors', 'Typography', 'Spacing', 'Shadows', 'Borders'],
            patterns: ['Layout Grids', 'Data Display', 'Feedback', 'Navigation'],
            documentation: ['Component Library', 'Usage Guidelines', 'Do\'s and Don\'ts']
        };
        
        this.researchMethods = {
            qualitative: ['User Interviews', 'Usability Testing', 'Card Sorting', 'Journey Mapping'],
            quantitative: ['A/B Testing', 'Analytics', 'Surveys', 'Heat Maps'],
            generative: ['Brainstorming', 'Sketching', 'Ideation', 'Concept Testing'],
            evaluative: ['Heuristic Evaluation', 'Accessibility Audit', 'Design Review']
        };
    }
    
    getSystemPrompt() {
        return `
You are a **Senior UI/UX Designer** with expertise in creating user-centered, accessible, and visually appealing digital experiences.

🎯 **Core Specializations:**
${this.capabilities.map(cap => `• ${cap}`).join('\n')}

🎨 **Design Principles:**
${Object.entries(this.designPrinciples).map(([category, principles]) => 
    `• ${category.toUpperCase()}: ${principles.join(', ')}`
).join('\n')}

🛠️ **Design Tools:**
${Object.entries(this.designTools).map(([category, tools]) => 
    `• ${category.toUpperCase()}: ${tools.join(', ')}`
).join('\n')}

🧩 **Design Systems:**
${Object.entries(this.designSystems).map(([category, elements]) => 
    `• ${category.toUpperCase()}: ${elements.join(', ')}`
).join('\n')}

🔍 **Research Methods:**
${Object.entries(this.researchMethods).map(([category, methods]) => 
    `• ${category.toUpperCase()}: ${methods.join(', ')}`
).join('\n')}

---

🎯 **Your Approach:**
1. **User-Centered**: Always start with user needs and goals
2. **Accessibility First**: Design for all users, including those with disabilities
3. **Data-Driven**: Base decisions on research and testing
4. **Iterative Process**: Design, test, learn, and improve
5. **Systematic Thinking**: Create scalable and consistent design systems
6. **Cross-Functional**: Collaborate effectively with developers and stakeholders

---

🚀 **Deliverables:**
- User research findings and personas
- Information architecture and user flows
- Wireframes and low-fidelity prototypes
- High-fidelity mockups and interactive prototypes
- Design systems and component libraries
- Accessibility guidelines and compliance reports
- Usability testing plans and results
- Design specifications and handoff documentation

---

💡 **Best Practices:**
- Follow WCAG 2.1 AA accessibility standards
- Maintain 4.5:1 color contrast ratios
- Design for mobile-first responsive layouts
- Use consistent spacing and typography scales
- Implement clear visual hierarchy
- Provide meaningful feedback for user actions
- Test designs with real users
- Document design decisions and rationale

Always create inclusive, usable, and beautiful digital experiences.
        `;
    }
    
    async executeTask(task) {
        const result = await super.executeTask(task);
        
        try {
            const taskType = this.analyzeTaskType(task.query);
            
            switch (taskType) {
                case 'user_research':
                    return await this.handleUserResearch(task);
                case 'wireframing':
                    return await this.handleWireframing(task);
                case 'ui_design':
                    return await this.handleUIDesign(task);
                case 'design_system':
                    return await this.handleDesignSystem(task);
                case 'accessibility_audit':
                    return await this.handleAccessibilityAudit(task);
                case 'usability_testing':
                    return await this.handleUsabilityTesting(task);
                case 'prototyping':
                    return await this.handlePrototyping(task);
                default:
                    return await this.handleGeneralDesignTask(task);
            }
        } catch (error) {
            this.logger.error('UI/UX Designer Agent task execution failed:', error);
            throw error;
        }
    }
    
    analyzeTaskType(query) {
        const lowerQuery = query.toLowerCase();
        
        if (lowerQuery.includes('research') || lowerQuery.includes('user') || lowerQuery.includes('persona')) {
            return 'user_research';
        } else if (lowerQuery.includes('wireframe') || lowerQuery.includes('layout') || lowerQuery.includes('structure')) {
            return 'wireframing';
        } else if (lowerQuery.includes('ui') || lowerQuery.includes('visual') || lowerQuery.includes('interface')) {
            return 'ui_design';
        } else if (lowerQuery.includes('design system') || lowerQuery.includes('component') || lowerQuery.includes('style guide')) {
            return 'design_system';
        } else if (lowerQuery.includes('accessibility') || lowerQuery.includes('a11y') || lowerQuery.includes('wcag')) {
            return 'accessibility_audit';
        } else if (lowerQuery.includes('usability') || lowerQuery.includes('testing') || lowerQuery.includes('user test')) {
            return 'usability_testing';
        } else if (lowerQuery.includes('prototype') || lowerQuery.includes('interactive') || lowerQuery.includes('mockup')) {
            return 'prototyping';
        }
        
        return 'general';
    }
    
    async handleUserResearch(task) {
        const prompt = `
As a UX Research Expert, help plan and conduct user research:

Task: ${task.query}

Please provide:
1. Research objectives and questions
2. Target user demographics and personas
3. Research methodology selection
4. Data collection instruments (surveys, interview guides)
5. Recruitment strategy
6. Analysis framework
7. Reporting template
8. Actionable insights and recommendations

Focus on gathering meaningful insights that inform design decisions.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleWireframing(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['layout', 'structure', 'navigation']);
        
        const prompt = `
As a UX Designer, help create wireframes and information architecture:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Information architecture and site map
2. User flow diagrams
3. Low-fidelity wireframes
4. Content hierarchy and organization
5. Navigation structure
6. Responsive layout considerations
7. Annotation and specifications
8. Next steps for high-fidelity design

Focus on structure, functionality, and user flow before visual design.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleUIDesign(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['ui', 'design', 'visual']);
        
        const prompt = `
As a UI Designer, help create visually appealing and functional interfaces:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Visual design direction and mood board
2. Color palette with accessibility considerations
3. Typography scale and hierarchy
4. Iconography and imagery guidelines
5. Component design specifications
6. Responsive design adaptations
7. Interaction states and micro-animations
8. Design handoff specifications

Focus on visual hierarchy, consistency, and brand alignment.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleDesignSystem(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['components', 'design', 'system']);
        
        const prompt = `
As a Design System Expert, help create scalable design systems:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Design token definitions (colors, typography, spacing)
2. Component library structure
3. Usage guidelines and documentation
4. Accessibility standards integration
5. Responsive behavior specifications
6. Version control and maintenance plan
7. Implementation guidelines for developers
8. Governance and contribution process

Focus on consistency, scalability, and developer-designer collaboration.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleAccessibilityAudit(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['accessibility', 'a11y']);
        
        const prompt = `
As an Accessibility Expert, help audit and improve accessibility:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. WCAG 2.1 compliance assessment
2. Color contrast analysis
3. Keyboard navigation evaluation
4. Screen reader compatibility check
5. Focus management review
6. Alternative text and labeling audit
7. Remediation recommendations with priorities
8. Testing plan with assistive technologies

Focus on creating inclusive experiences for all users.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleUsabilityTesting(task) {
        const prompt = `
As a Usability Testing Expert, help plan and conduct user testing:

Task: ${task.query}

Please provide:
1. Testing objectives and success metrics
2. Participant recruitment criteria
3. Task scenarios and test script
4. Testing methodology (moderated/unmoderated)
5. Data collection and analysis plan
6. Reporting template and metrics
7. Iteration recommendations
8. Follow-up testing strategy

Focus on gathering actionable insights for design improvements.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handlePrototyping(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['prototype', 'interactive']);
        
        const prompt = `
As a Prototyping Expert, help create interactive prototypes:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Prototype fidelity and scope definition
2. Interactive flow and state management
3. Animation and transition specifications
4. User testing integration
5. Stakeholder presentation format
6. Technical feasibility considerations
7. Handoff documentation for development
8. Iteration and feedback incorporation plan

Focus on validating design concepts and user interactions.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGeneralDesignTask(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Senior UI/UX Designer, help with this design task:

Task: ${task.query}

Context from codebase:
${context}

Please provide a comprehensive design solution following UX best practices and accessibility standards.
        `;
        
        return await this.generateResponse(prompt, task);
    }
}
