{"gemini": {"apiKey": "AIzaSyClkSi9-eCFh30dVZ6N31r6T2uUkt2Csus", "model": "gemini-2.0-flash-exp", "temperature": 0.7, "maxTokens": 8192, "timeout": 30000, "retries": 3}, "server": {"port": 3000, "host": "localhost", "nodeEnv": "development", "cors": {"origin": true, "credentials": true}}, "database": {"url": "./data/shai.db", "vectorDbPath": "./data/vectors", "backupInterval": 86400000, "maxBackups": 7}, "security": {"jwtSecret": "your_jwt_secret_here", "sessionSecret": "your_session_secret_here", "rateLimiting": {"windowMs": 900000, "max": 100}}, "features": {"enableWebUI": true, "enableCLI": true, "enableMemory": true, "enableContextEngine": true, "enableSubAgents": true}, "performance": {"maxConcurrentRequests": 10, "contextWindowSize": 32000, "memoryRetentionDays": 30, "cacheSize": 1000, "cacheTTL": 3600000}, "agents": {"maxSubAgents": 5, "taskTimeout": 300000, "retryAttempts": 3, "specializations": ["code-analysis", "code-generation", "testing", "documentation", "debugging", "security", "refactoring"]}, "contextEngine": {"maxFileSize": 10485760, "supportedExtensions": [".js", ".ts", ".jsx", ".tsx", ".vue", ".svelte", ".py", ".java", ".cpp", ".c", ".h", ".hpp", ".rs", ".go", ".php", ".rb", ".swift", ".kt", ".html", ".css", ".scss", ".sass", ".less", ".json", ".yaml", ".yml", ".xml", ".md", ".sql", ".sh", ".bat", ".ps1"], "excludePatterns": ["node_modules/**", ".git/**", "dist/**", "build/**", "*.log", "*.tmp", ".env*"], "embeddingDimensions": 1536, "chunkSize": 1000, "chunkOverlap": 200}, "logging": {"level": "info", "file": "./logs/shai.log", "enableConsole": true, "enableFile": true, "verbose": false, "maxFileSize": 10485760, "maxFiles": 5}, "development": {"debug": false, "hotReload": true, "mockResponses": false, "testMode": false}}