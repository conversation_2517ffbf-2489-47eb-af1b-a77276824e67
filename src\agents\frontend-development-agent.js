/**
 * Frontend Development Agent - Expert in UI frameworks, responsive design, and modern frontend tooling
 */

import { BaseAgent } from './base-agent.js';

export class FrontendDevelopmentAgent extends BaseAgent {
    constructor(orchestrator, logger) {
        super(orchestrator, logger);
        this.name = 'FrontendDevelopmentAgent';
        this.specialization = 'Frontend Development';
        this.capabilities = [
            'React/Vue/Angular Development',
            'Responsive Web Design',
            'State Management (Redux, Vuex, NgRx)',
            'Modern CSS (Tailwind, Styled Components)',
            'JavaScript/TypeScript Expertise',
            'Performance Optimization',
            'Accessibility (WCAG) Compliance',
            'Progressive Web Apps (PWA)',
            'Testing (Jest, Cypress, Testing Library)',
            'Build Tools (Webpack, Vite, Parcel)',
            'Component Libraries & Design Systems',
            'Mobile-First Development'
        ];
        
        this.frameworks = {
            react: ['React 18', 'Next.js', 'Gatsby', 'Remix'],
            vue: ['Vue 3', 'Nuxt.js', 'Quasar', 'Vite'],
            angular: ['Angular 17', 'Ionic', 'Angular Universal'],
            svelte: ['SvelteKit', 'Svelte'],
            static: ['HTML5', 'CSS3', 'Vanilla JS', '11ty', 'Astro']
        };
        
        this.stateManagement = {
            react: ['Redux Toolkit', 'Zustand', 'Jotai', 'Context API'],
            vue: ['Vuex', 'Pinia', 'Composition API'],
            angular: ['NgRx', 'Akita', 'Services'],
            general: ['MobX', 'RxJS', 'Recoil']
        };
        
        this.styling = {
            frameworks: ['Tailwind CSS', 'Bootstrap', 'Bulma', 'Chakra UI'],
            methodologies: ['CSS Modules', 'Styled Components', 'Emotion', 'SCSS/Sass'],
            tools: ['PostCSS', 'Autoprefixer', 'PurgeCSS', 'Stylelint']
        };
        
        this.buildTools = {
            bundlers: ['Vite', 'Webpack', 'Parcel', 'Rollup', 'esbuild'],
            transpilers: ['Babel', 'TypeScript', 'SWC'],
            linters: ['ESLint', 'Prettier', 'Stylelint'],
            testing: ['Jest', 'Vitest', 'Cypress', 'Playwright', 'Testing Library']
        };
    }
    
    getSystemPrompt() {
        return `
You are a **Senior Frontend Development Expert** with deep expertise in modern web development, UI frameworks, and user experience optimization.

🎯 **Core Specializations:**
${this.capabilities.map(cap => `• ${cap}`).join('\n')}

🛠️ **Technology Stack Expertise:**
**Frontend Frameworks:**
${Object.entries(this.frameworks).map(([category, frameworks]) => 
    `• ${category.toUpperCase()}: ${frameworks.join(', ')}`
).join('\n')}

**State Management:**
${Object.entries(this.stateManagement).map(([category, tools]) => 
    `• ${category.toUpperCase()}: ${tools.join(', ')}`
).join('\n')}

**Styling & Design:**
${Object.entries(this.styling).map(([category, tools]) => 
    `• ${category.toUpperCase()}: ${tools.join(', ')}`
).join('\n')}

**Build Tools & Testing:**
${Object.entries(this.buildTools).map(([category, tools]) => 
    `• ${category.toUpperCase()}: ${tools.join(', ')}`
).join('\n')}

---

🎨 **Your Approach:**
1. **User-Centric Design**: Focus on user experience and accessibility
2. **Performance First**: Optimize for Core Web Vitals and loading speed
3. **Responsive Design**: Mobile-first, cross-browser compatibility
4. **Component Architecture**: Reusable, maintainable component systems
5. **Modern Standards**: ES6+, TypeScript, semantic HTML
6. **Testing Strategy**: Unit, integration, and E2E testing

---

🚀 **Deliverables:**
- Complete frontend applications with modern architecture
- Responsive, accessible user interfaces
- Component libraries and design systems
- State management implementation
- Performance-optimized code
- Comprehensive testing suites
- Build configurations and deployment setups
- Documentation and style guides

---

💡 **Best Practices:**
- Follow React/Vue/Angular best practices
- Implement proper error boundaries and loading states
- Use semantic HTML and ARIA attributes
- Optimize images and assets
- Implement proper SEO and meta tags
- Follow CSS methodologies (BEM, OOCSS)
- Ensure cross-browser compatibility
- Implement proper security measures (CSP, XSS protection)

Always provide modern, accessible, and performant frontend solutions.
        `;
    }
    
    async executeTask(task) {
        const result = await super.executeTask(task);
        
        try {
            const taskType = this.analyzeTaskType(task.query);
            
            switch (taskType) {
                case 'component_development':
                    return await this.handleComponentDevelopment(task);
                case 'responsive_design':
                    return await this.handleResponsiveDesign(task);
                case 'state_management':
                    return await this.handleStateManagement(task);
                case 'performance_optimization':
                    return await this.handlePerformanceOptimization(task);
                case 'accessibility':
                    return await this.handleAccessibility(task);
                case 'testing':
                    return await this.handleTesting(task);
                case 'build_configuration':
                    return await this.handleBuildConfiguration(task);
                default:
                    return await this.handleGeneralFrontendTask(task);
            }
        } catch (error) {
            this.logger.error('Frontend Development Agent task execution failed:', error);
            throw error;
        }
    }
    
    analyzeTaskType(query) {
        const lowerQuery = query.toLowerCase();
        
        if (lowerQuery.includes('component') || lowerQuery.includes('react') || lowerQuery.includes('vue') || lowerQuery.includes('angular')) {
            return 'component_development';
        } else if (lowerQuery.includes('responsive') || lowerQuery.includes('mobile') || lowerQuery.includes('css') || lowerQuery.includes('layout')) {
            return 'responsive_design';
        } else if (lowerQuery.includes('state') || lowerQuery.includes('redux') || lowerQuery.includes('vuex') || lowerQuery.includes('context')) {
            return 'state_management';
        } else if (lowerQuery.includes('performance') || lowerQuery.includes('optimization') || lowerQuery.includes('loading')) {
            return 'performance_optimization';
        } else if (lowerQuery.includes('accessibility') || lowerQuery.includes('a11y') || lowerQuery.includes('wcag')) {
            return 'accessibility';
        } else if (lowerQuery.includes('test') || lowerQuery.includes('jest') || lowerQuery.includes('cypress')) {
            return 'testing';
        } else if (lowerQuery.includes('build') || lowerQuery.includes('webpack') || lowerQuery.includes('vite')) {
            return 'build_configuration';
        }
        
        return 'general';
    }
    
    async handleComponentDevelopment(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['components', 'jsx', 'vue', 'angular']);
        
        const prompt = `
As a Frontend Component Expert, help with component development:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Component architecture and structure
2. Props/interface definitions with TypeScript
3. State management within components
4. Event handling and lifecycle methods
5. Styling approach (CSS modules, styled-components, etc.)
6. Accessibility considerations
7. Testing examples
8. Documentation and usage examples

Focus on reusability, maintainability, and performance.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleResponsiveDesign(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['css', 'styles', 'responsive']);
        
        const prompt = `
As a Responsive Design Expert, help create mobile-first layouts:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Mobile-first CSS architecture
2. Breakpoint strategy and media queries
3. Flexible grid systems (CSS Grid, Flexbox)
4. Responsive typography and spacing
5. Image optimization and responsive images
6. Touch-friendly interactions
7. Cross-browser compatibility
8. Performance considerations

Focus on accessibility and user experience across all devices.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleStateManagement(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['state', 'store', 'redux', 'context']);
        
        const prompt = `
As a State Management Expert, help implement application state:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. State architecture and data flow
2. Actions, reducers, and selectors
3. Async operations and side effects
4. State normalization strategies
5. Performance optimizations
6. DevTools integration
7. Testing state logic
8. Migration strategies

Focus on predictable state updates and developer experience.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handlePerformanceOptimization(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['performance', 'optimization']);
        
        const prompt = `
As a Frontend Performance Expert, help optimize application performance:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Core Web Vitals optimization
2. Code splitting and lazy loading
3. Bundle size optimization
4. Image and asset optimization
5. Caching strategies
6. Runtime performance improvements
7. Memory leak prevention
8. Performance monitoring setup

Focus on measurable improvements in loading and runtime performance.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleAccessibility(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['accessibility', 'aria', 'a11y']);
        
        const prompt = `
As an Accessibility Expert, help implement WCAG-compliant interfaces:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Semantic HTML structure
2. ARIA attributes and roles
3. Keyboard navigation support
4. Screen reader compatibility
5. Color contrast and visual design
6. Focus management
7. Accessibility testing strategies
8. Documentation for accessibility features

Focus on WCAG 2.1 AA compliance and inclusive design.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleTesting(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['test', 'spec', 'cypress']);
        
        const prompt = `
As a Frontend Testing Expert, help implement comprehensive testing:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Unit testing for components and utilities
2. Integration testing for user flows
3. E2E testing scenarios
4. Visual regression testing
5. Accessibility testing
6. Performance testing
7. Test configuration and setup
8. CI/CD integration

Focus on reliable, maintainable tests that provide confidence.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleBuildConfiguration(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['webpack', 'vite', 'build']);
        
        const prompt = `
As a Build Configuration Expert, help optimize the build process:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Build tool configuration (Webpack, Vite, etc.)
2. Development server setup
3. Production optimization
4. Asset handling and optimization
5. Environment configuration
6. Hot module replacement
7. Deployment configuration
8. Performance monitoring

Focus on developer experience and production performance.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGeneralFrontendTask(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Senior Frontend Developer, help with this frontend development task:

Task: ${task.query}

Context from codebase:
${context}

Please provide a comprehensive solution following modern frontend development best practices.
        `;
        
        return await this.generateResponse(prompt, task);
    }
}
