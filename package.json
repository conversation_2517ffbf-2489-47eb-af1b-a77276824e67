{"name": "shai-ai-assistant", "version": "1.0.0", "description": "Advanced AI Coding Assistant powered by Gemini 2.0 Flash with multi-agent architecture", "main": "src/index.js", "type": "module", "scripts": {"start": "node start-shai.js", "dev": "node --watch start-shai.js", "cli": "node start-shai.js cli", "web": "node start-shai.js web", "daemon": "node start-shai.js daemon", "test": "node test-startup.js", "test:watch": "node --watch test-startup.js", "verify": "node verify-fix.js", "install-deps": "node install.js", "setup": "node scripts/setup.js", "lint": "echo 'Code quality linting available through SHAI'", "format": "echo 'Code formatting available through SHAI'", "build": "echo 'No build step required for Node.js application'", "clean": "rm -rf node_modules bun.lockb && bun install", "docs": "echo 'Documentation generation available through SHAI'", "security": "echo 'Security audit available through SHAI'"}, "keywords": ["ai", "coding-assistant", "gemini", "multi-agent", "nodejs", "cli", "web-ui"], "author": "<PERSON>hai AI Assistant", "license": "MIT", "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "repository": {"type": "git", "url": "https://github.com/shai-ai/assistant.git"}, "bugs": {"url": "https://github.com/shai-ai/assistant/issues"}, "homepage": "https://github.com/shai-ai/assistant#readme", "dependencies": {"@google/generative-ai": "^0.21.0", "@babel/parser": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9", "boxen": "^8.0.1", "chalk": "^5.3.0", "chokidar": "^4.0.1", "commander": "^12.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "figlet": "^1.8.0", "glob": "^11.0.0", "gradient-string": "^3.0.0", "helmet": "^8.0.0", "inquirer": "^12.1.0", "node-pty": "^1.1.0", "ora": "^8.1.1", "simple-git": "^3.27.0", "socket.io": "^4.8.1", "uuid": "^11.0.3", "acorn": "^8.14.0", "acorn-walk": "^8.3.4"}, "devDependencies": {"@types/node": "^22.10.2"}, "trustedDependencies": ["@google/generative-ai", "node-pty"]}