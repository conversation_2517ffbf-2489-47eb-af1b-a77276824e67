{"name": "shai-ai-assistant", "version": "1.0.0", "description": "Advanced AI Coding Assistant powered by Gemini 2.0 Flash with multi-agent architecture", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "cli": "node src/cli/index.js", "web": "node src/web/server.js", "test": "jest", "test:watch": "jest --watch", "build": "npm run build:web", "build:web": "cd src/ui/web && npm run build", "install:web": "cd src/ui/web && npm install", "setup": "npm install && npm run install:web"}, "keywords": ["ai", "coding-assistant", "gemini", "multi-agent", "nodejs", "cli", "web-ui"], "author": "<PERSON>hai AI Assistant", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/shai-ai/assistant.git"}, "bugs": {"url": "https://github.com/shai-ai/assistant/issues"}, "homepage": "https://github.com/shai-ai/assistant#readme", "devDependencies": {}}