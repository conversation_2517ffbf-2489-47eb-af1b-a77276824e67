/**
 * Web Search Tool - Direct web crawling and content extraction without external APIs
 */

import https from 'https';
import http from 'http';
import { URL } from 'url';
import { EventEmitter } from 'events';

export class WebSearchTool extends EventEmitter {
    constructor(logger) {
        super();
        this.logger = logger.child('WebSearch');
        this.cache = new Map();
        this.rateLimiter = new Map();
        this.userAgent = 'SHAI-AI-Assistant/1.0 (Educational/Research Purpose)';
        this.maxConcurrentRequests = 5;
        this.requestTimeout = 10000;
        this.maxRetries = 3;
        this.activeRequests = 0;
        this.requestQueue = [];
        
        // Common search engines and documentation sites
        this.searchSources = {
            documentation: [
                'https://developer.mozilla.org',
                'https://docs.python.org',
                'https://nodejs.org/docs',
                'https://reactjs.org/docs',
                'https://vuejs.org/guide',
                'https://angular.io/docs',
                'https://docs.djangoproject.com',
                'https://flask.palletsprojects.com',
                'https://spring.io/guides',
                'https://docs.microsoft.com',
                'https://cloud.google.com/docs',
                'https://docs.aws.amazon.com'
            ],
            repositories: [
                'https://github.com',
                'https://gitlab.com',
                'https://bitbucket.org'
            ],
            learning: [
                'https://stackoverflow.com',
                'https://dev.to',
                'https://medium.com',
                'https://www.freecodecamp.org',
                'https://css-tricks.com'
            ]
        };
        
        // Content extractors for different site types
        this.extractors = {
            github: this.extractGitHubContent.bind(this),
            stackoverflow: this.extractStackOverflowContent.bind(this),
            documentation: this.extractDocumentationContent.bind(this),
            general: this.extractGeneralContent.bind(this)
        };
    }
    
    async search(query, options = {}) {
        try {
            const {
                maxResults = 10,
                sources = ['documentation', 'repositories', 'learning'],
                includeCode = true,
                language = null,
                freshness = 'any' // any, day, week, month
            } = options;
            
            this.logger.info(`Searching for: "${query}" with options:`, options);
            
            const searchResults = [];
            const searchPromises = [];
            
            // Search across different source types
            for (const sourceType of sources) {
                if (this.searchSources[sourceType]) {
                    for (const baseUrl of this.searchSources[sourceType]) {
                        searchPromises.push(
                            this.searchSite(baseUrl, query, { includeCode, language })
                                .catch(error => {
                                    this.logger.warn(`Search failed for ${baseUrl}:`, error.message);
                                    return [];
                                })
                        );
                    }
                }
            }
            
            // Execute searches with concurrency control
            const results = await this.executeConcurrentRequests(searchPromises);
            
            // Flatten and deduplicate results
            const allResults = results.flat().filter(Boolean);
            const uniqueResults = this.deduplicateResults(allResults);
            
            // Score and rank results
            const rankedResults = this.rankResults(uniqueResults, query);
            
            // Return top results
            return rankedResults.slice(0, maxResults);
            
        } catch (error) {
            this.logger.error('Web search failed:', error);
            throw error;
        }
    }
    
    async searchSite(baseUrl, query, options = {}) {
        try {
            const siteType = this.detectSiteType(baseUrl);
            const searchUrl = this.buildSearchUrl(baseUrl, query, siteType);
            
            if (!searchUrl) {
                this.logger.debug(`No search URL builder for ${baseUrl}`);
                return [];
            }
            
            const content = await this.fetchContent(searchUrl);
            const extractor = this.extractors[siteType] || this.extractors.general;
            
            return await extractor(content, query, options);
            
        } catch (error) {
            this.logger.warn(`Site search failed for ${baseUrl}:`, error.message);
            return [];
        }
    }
    
    buildSearchUrl(baseUrl, query, siteType) {
        const encodedQuery = encodeURIComponent(query);
        
        switch (siteType) {
            case 'github':
                return `${baseUrl}/search?q=${encodedQuery}&type=code`;
            case 'stackoverflow':
                return `${baseUrl}/search?q=${encodedQuery}`;
            case 'documentation':
                // Try to find site-specific search
                if (baseUrl.includes('developer.mozilla.org')) {
                    return `${baseUrl}/en-US/search?q=${encodedQuery}`;
                } else if (baseUrl.includes('docs.python.org')) {
                    return `${baseUrl}/3/search.html?q=${encodedQuery}`;
                } else if (baseUrl.includes('nodejs.org')) {
                    return `${baseUrl}/api/all.html#all_${encodedQuery}`;
                }
                return `${baseUrl}/search?q=${encodedQuery}`;
            default:
                return `${baseUrl}/search?q=${encodedQuery}`;
        }
    }
    
    detectSiteType(url) {
        if (url.includes('github.com')) return 'github';
        if (url.includes('stackoverflow.com')) return 'stackoverflow';
        if (url.includes('docs.') || url.includes('developer.')) return 'documentation';
        return 'general';
    }
    
    async fetchContent(url, retries = 0) {
        return new Promise((resolve, reject) => {
            // Check cache first
            if (this.cache.has(url)) {
                const cached = this.cache.get(url);
                if (Date.now() - cached.timestamp < 3600000) { // 1 hour cache
                    return resolve(cached.content);
                }
            }
            
            // Rate limiting
            const domain = new URL(url).hostname;
            const lastRequest = this.rateLimiter.get(domain) || 0;
            const minInterval = 1000; // 1 second between requests to same domain
            
            if (Date.now() - lastRequest < minInterval) {
                setTimeout(() => {
                    this.fetchContent(url, retries).then(resolve).catch(reject);
                }, minInterval - (Date.now() - lastRequest));
                return;
            }
            
            this.rateLimiter.set(domain, Date.now());
            
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;
            
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': this.userAgent,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                timeout: this.requestTimeout
            };
            
            const req = client.request(options, (res) => {
                let data = '';
                
                // Handle redirects
                if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
                    const redirectUrl = new URL(res.headers.location, url).href;
                    return this.fetchContent(redirectUrl, retries).then(resolve).catch(reject);
                }
                
                if (res.statusCode !== 200) {
                    return reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                }
                
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    // Cache the result
                    this.cache.set(url, {
                        content: data,
                        timestamp: Date.now()
                    });
                    
                    resolve(data);
                });
            });
            
            req.on('error', (error) => {
                if (retries < this.maxRetries) {
                    setTimeout(() => {
                        this.fetchContent(url, retries + 1).then(resolve).catch(reject);
                    }, Math.pow(2, retries) * 1000);
                } else {
                    reject(error);
                }
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });
            
            req.end();
        });
    }
    
    async extractGitHubContent(html, query, options) {
        const results = [];
        
        try {
            // Simple HTML parsing without external dependencies
            const codeBlocks = this.extractMatches(html, /<div class="code-list-item[^>]*>[\s\S]*?<\/div>/g);
            
            for (const block of codeBlocks.slice(0, 5)) {
                const title = this.extractText(block, /<a[^>]*class="[^"]*Link--primary[^"]*"[^>]*>(.*?)<\/a>/);
                const description = this.extractText(block, /<p[^>]*>(.*?)<\/p>/);
                const url = this.extractAttribute(block, /<a[^>]*href="([^"]*)"/, 'href');
                
                if (title && url) {
                    results.push({
                        title: this.cleanText(title),
                        description: this.cleanText(description) || '',
                        url: url.startsWith('/') ? `https://github.com${url}` : url,
                        source: 'GitHub',
                        type: 'code',
                        relevance: this.calculateRelevance(title + ' ' + description, query)
                    });
                }
            }
        } catch (error) {
            this.logger.warn('GitHub content extraction failed:', error);
        }
        
        return results;
    }
    
    async extractStackOverflowContent(html, query, options) {
        const results = [];
        
        try {
            const questionBlocks = this.extractMatches(html, /<div class="question-summary[^>]*>[\s\S]*?<\/div>/g);
            
            for (const block of questionBlocks.slice(0, 5)) {
                const title = this.extractText(block, /<h3[^>]*><a[^>]*>(.*?)<\/a><\/h3>/);
                const excerpt = this.extractText(block, /<div class="excerpt"[^>]*>(.*?)<\/div>/);
                const url = this.extractAttribute(block, /<h3[^>]*><a[^>]*href="([^"]*)"/, 'href');
                const votes = this.extractText(block, /<span class="vote-count-post[^>]*>(.*?)<\/span>/);
                
                if (title && url) {
                    results.push({
                        title: this.cleanText(title),
                        description: this.cleanText(excerpt) || '',
                        url: url.startsWith('/') ? `https://stackoverflow.com${url}` : url,
                        source: 'Stack Overflow',
                        type: 'qa',
                        votes: parseInt(votes) || 0,
                        relevance: this.calculateRelevance(title + ' ' + excerpt, query)
                    });
                }
            }
        } catch (error) {
            this.logger.warn('Stack Overflow content extraction failed:', error);
        }
        
        return results;
    }
    
    async extractDocumentationContent(html, query, options) {
        const results = [];
        
        try {
            // Look for common documentation patterns
            const sections = this.extractMatches(html, /<section[^>]*>[\s\S]*?<\/section>|<article[^>]*>[\s\S]*?<\/article>|<div class="[^"]*doc[^"]*"[^>]*>[\s\S]*?<\/div>/g);
            
            for (const section of sections.slice(0, 5)) {
                const title = this.extractText(section, /<h[1-6][^>]*>(.*?)<\/h[1-6]>/) ||
                             this.extractText(section, /<title[^>]*>(.*?)<\/title>/);
                const content = this.extractText(section, /<p[^>]*>(.*?)<\/p>/) ||
                               this.extractText(section, /<div[^>]*>(.*?)<\/div>/).substring(0, 200);
                
                if (title && content && this.calculateRelevance(title + ' ' + content, query) > 0.3) {
                    results.push({
                        title: this.cleanText(title),
                        description: this.cleanText(content),
                        url: '', // Would need to be extracted from context
                        source: 'Documentation',
                        type: 'docs',
                        relevance: this.calculateRelevance(title + ' ' + content, query)
                    });
                }
            }
        } catch (error) {
            this.logger.warn('Documentation content extraction failed:', error);
        }
        
        return results;
    }
    
    async extractGeneralContent(html, query, options) {
        const results = [];
        
        try {
            // Extract general content patterns
            const title = this.extractText(html, /<title[^>]*>(.*?)<\/title>/);
            const description = this.extractText(html, /<meta[^>]*name="description"[^>]*content="([^"]*)"/) ||
                              this.extractText(html, /<p[^>]*>(.*?)<\/p>/).substring(0, 200);
            
            if (title && this.calculateRelevance(title + ' ' + description, query) > 0.2) {
                results.push({
                    title: this.cleanText(title),
                    description: this.cleanText(description),
                    url: '',
                    source: 'Web',
                    type: 'general',
                    relevance: this.calculateRelevance(title + ' ' + description, query)
                });
            }
        } catch (error) {
            this.logger.warn('General content extraction failed:', error);
        }
        
        return results;
    }
    
    extractMatches(text, regex) {
        const matches = [];
        let match;
        while ((match = regex.exec(text)) !== null) {
            matches.push(match[0]);
        }
        return matches;
    }
    
    extractText(html, regex) {
        const match = html.match(regex);
        return match ? match[1] : null;
    }
    
    extractAttribute(html, regex, attr) {
        const match = html.match(regex);
        return match ? match[1] : null;
    }
    
    cleanText(text) {
        if (!text) return '';
        return text
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/&[^;]+;/g, ' ') // Remove HTML entities
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
    }
    
    calculateRelevance(text, query) {
        if (!text || !query) return 0;
        
        const textLower = text.toLowerCase();
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(/\s+/);
        
        let score = 0;
        let totalWords = queryWords.length;
        
        // Exact phrase match
        if (textLower.includes(queryLower)) {
            score += 0.5;
        }
        
        // Individual word matches
        for (const word of queryWords) {
            if (textLower.includes(word)) {
                score += 0.3;
            }
        }
        
        return Math.min(score / totalWords, 1.0);
    }
    
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = result.url || result.title;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    
    rankResults(results, query) {
        return results
            .sort((a, b) => {
                // Primary sort by relevance
                if (b.relevance !== a.relevance) {
                    return b.relevance - a.relevance;
                }
                
                // Secondary sort by votes (if available)
                if (b.votes !== undefined && a.votes !== undefined) {
                    return b.votes - a.votes;
                }
                
                // Tertiary sort by source priority
                const sourcePriority = {
                    'Documentation': 3,
                    'Stack Overflow': 2,
                    'GitHub': 2,
                    'Web': 1
                };
                
                return (sourcePriority[b.source] || 0) - (sourcePriority[a.source] || 0);
            });
    }
    
    async executeConcurrentRequests(promises) {
        const results = [];
        const executing = [];
        
        for (const promise of promises) {
            const p = Promise.resolve(promise).then(result => {
                executing.splice(executing.indexOf(p), 1);
                return result;
            });
            
            results.push(p);
            
            if (promises.length >= this.maxConcurrentRequests) {
                executing.push(p);
                
                if (executing.length >= this.maxConcurrentRequests) {
                    await Promise.race(executing);
                }
            }
        }
        
        return Promise.all(results);
    }
    
    clearCache() {
        this.cache.clear();
        this.logger.info('Web search cache cleared');
    }
    
    getCacheStats() {
        return {
            size: this.cache.size,
            entries: Array.from(this.cache.keys())
        };
    }
}
