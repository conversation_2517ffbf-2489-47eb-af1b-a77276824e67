/**
 * DevOps & Infrastructure Agent - Expert in deployment, CI/CD, containerization, and cloud services
 */

import { BaseAgent } from './base-agent.js';

export class DevOpsInfrastructureAgent extends BaseAgent {
    constructor(orchestrator, logger) {
        super(orchestrator, logger);
        this.name = 'DevOpsInfrastructureAgent';
        this.specialization = 'DevOps & Infrastructure';
        this.capabilities = [
            'CI/CD Pipeline Design & Implementation',
            'Containerization (Docker, Kubernetes)',
            'Cloud Infrastructure (AWS, Azure, GCP)',
            'Infrastructure as Code (Terraform, CloudFormation)',
            'Monitoring & Observability',
            'Security & Compliance',
            'Performance Optimization',
            'Disaster Recovery & Backup',
            'Configuration Management',
            'Automated Testing & Deployment',
            'Cost Optimization',
            'Scalability & Load Balancing'
        ];
        
        this.cloudProviders = {
            aws: {
                compute: ['EC2', 'Lambda', 'ECS', 'EKS', 'Fargate'],
                storage: ['S3', 'EBS', 'EFS', 'Glacier'],
                database: ['RDS', 'DynamoDB', 'ElastiCache', 'DocumentDB'],
                networking: ['VPC', 'CloudFront', 'Route 53', 'API Gateway'],
                monitoring: ['CloudWatch', 'X-Ray', 'CloudTrail']
            },
            azure: {
                compute: ['Virtual Machines', 'App Service', 'Container Instances', 'AKS'],
                storage: ['Blob Storage', 'File Storage', 'Disk Storage'],
                database: ['SQL Database', 'Cosmos DB', 'Cache for Redis'],
                networking: ['Virtual Network', 'Load Balancer', 'Application Gateway'],
                monitoring: ['Monitor', 'Application Insights', 'Log Analytics']
            },
            gcp: {
                compute: ['Compute Engine', 'App Engine', 'Cloud Run', 'GKE'],
                storage: ['Cloud Storage', 'Persistent Disk', 'Filestore'],
                database: ['Cloud SQL', 'Firestore', 'Bigtable', 'Memorystore'],
                networking: ['VPC', 'Cloud Load Balancing', 'Cloud CDN'],
                monitoring: ['Cloud Monitoring', 'Cloud Logging', 'Cloud Trace']
            }
        };
        
        this.tools = {
            containerization: ['Docker', 'Podman', 'containerd', 'CRI-O'],
            orchestration: ['Kubernetes', 'Docker Swarm', 'Nomad', 'OpenShift'],
            cicd: ['Jenkins', 'GitLab CI', 'GitHub Actions', 'Azure DevOps', 'CircleCI'],
            iac: ['Terraform', 'CloudFormation', 'Pulumi', 'CDK', 'Ansible'],
            monitoring: ['Prometheus', 'Grafana', 'ELK Stack', 'Datadog', 'New Relic'],
            security: ['Vault', 'SOPS', 'Falco', 'Twistlock', 'Aqua Security']
        };
        
        this.practices = {
            deployment: ['Blue-Green', 'Canary', 'Rolling', 'A/B Testing'],
            testing: ['Unit Testing', 'Integration Testing', 'E2E Testing', 'Load Testing'],
            security: ['SAST', 'DAST', 'Container Scanning', 'Dependency Scanning'],
            monitoring: ['Metrics', 'Logs', 'Traces', 'Alerts', 'SLI/SLO']
        };
    }
    
    getSystemPrompt() {
        return `
You are a **Senior DevOps & Infrastructure Expert** with deep expertise in cloud platforms, automation, and scalable system operations.

🎯 **Core Specializations:**
${this.capabilities.map(cap => `• ${cap}`).join('\n')}

☁️ **Cloud Platforms:**
${Object.entries(this.cloudProviders).map(([provider, services]) => 
    `**${provider.toUpperCase()}:**\n${Object.entries(services).map(([category, items]) => 
        `  • ${category}: ${items.join(', ')}`
    ).join('\n')}`
).join('\n\n')}

🛠️ **DevOps Tools:**
${Object.entries(this.tools).map(([category, tools]) => 
    `• ${category.toUpperCase()}: ${tools.join(', ')}`
).join('\n')}

📋 **Best Practices:**
${Object.entries(this.practices).map(([category, practices]) => 
    `• ${category.toUpperCase()}: ${practices.join(', ')}`
).join('\n')}

---

🏗️ **Your Approach:**
1. **Infrastructure as Code**: Everything should be version-controlled and reproducible
2. **Automation First**: Automate repetitive tasks and processes
3. **Security by Design**: Implement security at every layer
4. **Observability**: Comprehensive monitoring, logging, and alerting
5. **Scalability**: Design for growth and handle traffic spikes
6. **Cost Optimization**: Balance performance with cost efficiency
7. **Disaster Recovery**: Plan for failures and ensure business continuity

---

🚀 **Deliverables:**
- Complete CI/CD pipeline configurations
- Infrastructure as Code templates
- Container and orchestration setups
- Monitoring and alerting systems
- Security and compliance implementations
- Deployment strategies and rollback procedures
- Documentation and runbooks
- Cost optimization recommendations

---

💡 **Best Practices:**
- Follow the 12-factor app methodology
- Implement proper secret management
- Use immutable infrastructure patterns
- Apply least privilege security principles
- Monitor everything with proper alerting
- Implement proper backup and disaster recovery
- Use infrastructure as code for all resources
- Maintain comprehensive documentation

Always provide production-ready, secure, and scalable infrastructure solutions.
        `;
    }
    
    async executeTask(task) {
        const result = await super.executeTask(task);
        
        try {
            const taskType = this.analyzeTaskType(task.query);
            
            switch (taskType) {
                case 'cicd_pipeline':
                    return await this.handleCICDPipeline(task);
                case 'containerization':
                    return await this.handleContainerization(task);
                case 'cloud_infrastructure':
                    return await this.handleCloudInfrastructure(task);
                case 'monitoring':
                    return await this.handleMonitoring(task);
                case 'security':
                    return await this.handleSecurity(task);
                case 'deployment':
                    return await this.handleDeployment(task);
                case 'scaling':
                    return await this.handleScaling(task);
                case 'cost_optimization':
                    return await this.handleCostOptimization(task);
                default:
                    return await this.handleGeneralDevOpsTask(task);
            }
        } catch (error) {
            this.logger.error('DevOps Infrastructure Agent task execution failed:', error);
            throw error;
        }
    }
    
    analyzeTaskType(query) {
        const lowerQuery = query.toLowerCase();
        
        if (lowerQuery.includes('ci/cd') || lowerQuery.includes('pipeline') || lowerQuery.includes('deployment pipeline')) {
            return 'cicd_pipeline';
        } else if (lowerQuery.includes('docker') || lowerQuery.includes('container') || lowerQuery.includes('kubernetes')) {
            return 'containerization';
        } else if (lowerQuery.includes('cloud') || lowerQuery.includes('aws') || lowerQuery.includes('azure') || lowerQuery.includes('gcp')) {
            return 'cloud_infrastructure';
        } else if (lowerQuery.includes('monitoring') || lowerQuery.includes('observability') || lowerQuery.includes('metrics')) {
            return 'monitoring';
        } else if (lowerQuery.includes('security') || lowerQuery.includes('compliance') || lowerQuery.includes('vulnerability')) {
            return 'security';
        } else if (lowerQuery.includes('deploy') || lowerQuery.includes('release') || lowerQuery.includes('rollout')) {
            return 'deployment';
        } else if (lowerQuery.includes('scale') || lowerQuery.includes('load balancing') || lowerQuery.includes('autoscaling')) {
            return 'scaling';
        } else if (lowerQuery.includes('cost') || lowerQuery.includes('optimization') || lowerQuery.includes('budget')) {
            return 'cost_optimization';
        }
        
        return 'general';
    }
    
    async handleCICDPipeline(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['ci', 'cd', 'pipeline', 'deploy']);
        
        const prompt = `
As a CI/CD Expert, help design and implement deployment pipelines:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. **Pipeline Architecture**
   - Source control integration
   - Build and test stages
   - Deployment environments
   - Approval processes

2. **Implementation**
   - Pipeline configuration files
   - Build scripts and automation
   - Test automation integration
   - Artifact management

3. **Security & Quality**
   - Code quality gates
   - Security scanning integration
   - Secret management
   - Compliance checks

4. **Monitoring & Notifications**
   - Pipeline monitoring
   - Failure notifications
   - Deployment tracking
   - Rollback procedures

Focus on reliability, security, and developer experience.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleContainerization(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['docker', 'container', 'kubernetes']);
        
        const prompt = `
As a Containerization Expert, help with container and orchestration setup:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. **Container Strategy**
   - Dockerfile optimization
   - Multi-stage builds
   - Base image selection
   - Security best practices

2. **Orchestration**
   - Kubernetes manifests
   - Service mesh configuration
   - Ingress and networking
   - Storage and persistence

3. **Operations**
   - Health checks and probes
   - Resource limits and requests
   - Scaling policies
   - Update strategies

4. **Monitoring & Debugging**
   - Container monitoring
   - Log aggregation
   - Debugging tools
   - Performance optimization

Focus on production-ready, secure, and scalable containerization.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleCloudInfrastructure(task) {
        const prompt = `
As a Cloud Infrastructure Expert, help design and implement cloud solutions:

Task: ${task.query}

Please provide:
1. **Architecture Design**
   - High-level infrastructure diagram
   - Service selection and sizing
   - Network architecture
   - Security boundaries

2. **Infrastructure as Code**
   - Terraform/CloudFormation templates
   - Resource provisioning
   - Environment management
   - State management

3. **Security & Compliance**
   - IAM policies and roles
   - Network security groups
   - Encryption at rest and in transit
   - Compliance frameworks

4. **Cost & Performance**
   - Resource optimization
   - Cost monitoring and alerts
   - Performance tuning
   - Disaster recovery planning

Focus on scalability, security, and cost-effectiveness.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleMonitoring(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['monitoring', 'metrics', 'logs']);
        
        const prompt = `
As a Monitoring & Observability Expert, help implement comprehensive monitoring:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. **Monitoring Strategy**
   - Metrics collection and storage
   - Log aggregation and analysis
   - Distributed tracing
   - SLI/SLO definition

2. **Implementation**
   - Monitoring stack setup
   - Dashboard configuration
   - Alert rules and thresholds
   - Notification channels

3. **Observability**
   - Application performance monitoring
   - Infrastructure monitoring
   - Business metrics tracking
   - Error tracking and analysis

4. **Operations**
   - Incident response procedures
   - Runbook automation
   - Capacity planning
   - Performance optimization

Focus on proactive monitoring and rapid incident resolution.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleSecurity(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['security', 'auth', 'compliance']);
        
        const prompt = `
As a DevSecOps Expert, help implement security and compliance:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. **Security Architecture**
   - Threat modeling and risk assessment
   - Security controls implementation
   - Identity and access management
   - Network security design

2. **Security Automation**
   - Security scanning in CI/CD
   - Vulnerability management
   - Compliance automation
   - Security testing integration

3. **Operational Security**
   - Secret management
   - Security monitoring and alerting
   - Incident response procedures
   - Security training and awareness

4. **Compliance**
   - Regulatory compliance frameworks
   - Audit trail and logging
   - Data protection and privacy
   - Security documentation

Focus on security by design and continuous compliance.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleDeployment(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['deploy', 'release']);
        
        const prompt = `
As a Deployment Expert, help implement deployment strategies:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. **Deployment Strategy**
   - Deployment pattern selection
   - Environment promotion
   - Release planning
   - Rollback procedures

2. **Implementation**
   - Deployment scripts and automation
   - Configuration management
   - Database migrations
   - Feature flag integration

3. **Quality Assurance**
   - Pre-deployment testing
   - Smoke tests and health checks
   - Performance validation
   - User acceptance testing

4. **Operations**
   - Deployment monitoring
   - Post-deployment verification
   - Issue resolution procedures
   - Communication and documentation

Focus on reliable, zero-downtime deployments.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleScaling(task) {
        const prompt = `
As a Scalability Expert, help implement scaling solutions:

Task: ${task.query}

Please provide:
1. **Scaling Strategy**
   - Horizontal vs vertical scaling
   - Auto-scaling policies
   - Load balancing configuration
   - Performance bottleneck analysis

2. **Implementation**
   - Auto-scaling group configuration
   - Load balancer setup
   - Database scaling strategies
   - Caching implementation

3. **Monitoring & Optimization**
   - Performance metrics tracking
   - Capacity planning
   - Cost optimization
   - Resource utilization analysis

4. **Testing & Validation**
   - Load testing procedures
   - Stress testing scenarios
   - Performance benchmarking
   - Scaling validation

Focus on efficient resource utilization and cost-effective scaling.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleCostOptimization(task) {
        const prompt = `
As a Cloud Cost Optimization Expert, help reduce infrastructure costs:

Task: ${task.query}

Please provide:
1. **Cost Analysis**
   - Current cost breakdown
   - Usage pattern analysis
   - Waste identification
   - Optimization opportunities

2. **Optimization Strategies**
   - Right-sizing recommendations
   - Reserved instance planning
   - Spot instance utilization
   - Storage optimization

3. **Implementation**
   - Cost monitoring setup
   - Budget alerts and controls
   - Automated cost optimization
   - Resource lifecycle management

4. **Governance**
   - Cost allocation and tagging
   - Spending policies
   - Regular cost reviews
   - Team cost awareness

Focus on maintaining performance while reducing costs.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGeneralDevOpsTask(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Senior DevOps Engineer, help with this infrastructure task:

Task: ${task.query}

Context from codebase:
${context}

Please provide a comprehensive DevOps solution following best practices for automation, security, and scalability.
        `;
        
        return await this.generateResponse(prompt, task);
    }
}
