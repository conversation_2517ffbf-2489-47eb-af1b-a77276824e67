# Bun configuration for SHAI AI Assistant

[install]
# Use exact versions for better reproducibility
exact = true

# Cache directory
cache = "node_modules/.cache/bun"

# Registry configuration
registry = "https://registry.npmjs.org/"

# Trusted dependencies that can run install scripts
trusted = [
  "@google/generative-ai",
  "node-pty"
]

[install.scopes]
# Scope configurations if needed

[run]
# Environment variables for bun run
env = "development"

# Shell to use for scripts
shell = "system"

[test]
# Test configuration
preload = []
coverage = true

[build]
# Build configuration
target = "node"
format = "esm"
splitting = false
