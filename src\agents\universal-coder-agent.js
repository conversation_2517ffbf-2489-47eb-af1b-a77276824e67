/**
 * Universal Coder Agent - Multi-language expert for complete project creation
 */

import { BaseAgent } from './base-agent.js';

export class UniversalCoderAgent extends BaseAgent {
    constructor(orchestrator, logger) {
        super(orchestrator, logger);
        this.name = 'UniversalCoderAgent';
        this.specialization = 'Universal Programming';
        this.capabilities = [
            'Multi-Language Project Creation',
            'Full-Stack Application Development',
            'Cross-Platform Development',
            'Architecture Design & Implementation',
            'Database Design & Integration',
            'API Development & Integration',
            'Testing & Quality Assurance',
            'DevOps & Deployment',
            'Performance Optimization',
            'Security Implementation',
            'Documentation Generation',
            'Code Migration & Refactoring'
        ];
        
        this.languages = {
            web: {
                frontend: ['JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'React', 'Vue.js', 'Angular', 'Svelte'],
                backend: ['Node.js', 'Python', 'PHP', 'Ruby', 'Java', 'C#', 'Go', 'Rust']
            },
            mobile: {
                native: ['Swift (iOS)', 'Kotlin/Java (Android)', 'Objective-C'],
                crossPlatform: ['React Native', 'Flutter', 'Xamarin', 'Ionic', 'Cordova']
            },
            desktop: {
                native: ['C++', 'C#', 'Java', 'Swift', 'Rust'],
                crossPlatform: ['Electron', 'Tauri', 'Qt', 'Flutter Desktop', '.NET MAUI']
            },
            systems: ['C', 'C++', 'Rust', 'Go', 'Assembly'],
            data: ['Python', 'R', 'Julia', 'Scala', 'SQL'],
            functional: ['Haskell', 'Clojure', 'F#', 'Erlang', 'Elixir'],
            scripting: ['Python', 'Bash', 'PowerShell', 'Perl', 'Ruby']
        };
        
        this.frameworks = {
            web: ['Express.js', 'Django', 'Flask', 'Spring Boot', 'ASP.NET Core', 'Laravel', 'Ruby on Rails'],
            mobile: ['React Native', 'Flutter', 'Xamarin', 'Ionic'],
            desktop: ['Electron', 'Qt', 'WPF', 'JavaFX', 'Tkinter'],
            game: ['Unity', 'Unreal Engine', 'Godot', 'Pygame', 'LibGDX'],
            ml: ['TensorFlow', 'PyTorch', 'Scikit-learn', 'Keras', 'OpenCV']
        };
        
        this.databases = {
            relational: ['PostgreSQL', 'MySQL', 'SQLite', 'SQL Server', 'Oracle'],
            nosql: ['MongoDB', 'Redis', 'Cassandra', 'DynamoDB', 'CouchDB'],
            graph: ['Neo4j', 'ArangoDB', 'Amazon Neptune'],
            timeseries: ['InfluxDB', 'TimescaleDB'],
            search: ['Elasticsearch', 'Solr']
        };
        
        this.cloudPlatforms = {
            aws: ['EC2', 'Lambda', 'S3', 'RDS', 'DynamoDB', 'API Gateway'],
            azure: ['App Service', 'Functions', 'Cosmos DB', 'SQL Database'],
            gcp: ['App Engine', 'Cloud Functions', 'Firestore', 'Cloud SQL'],
            others: ['Heroku', 'Vercel', 'Netlify', 'DigitalOcean', 'Railway']
        };
    }
    
    getSystemPrompt() {
        return `
You are a **Universal Programming Expert** capable of creating complete applications in any programming language or framework from scratch.

🎯 **Core Specializations:**
${this.capabilities.map(cap => `• ${cap}`).join('\n')}

💻 **Programming Languages:**
${Object.entries(this.languages).map(([category, langs]) => {
    if (typeof langs === 'object' && !Array.isArray(langs)) {
        return Object.entries(langs).map(([subcat, sublang]) => 
            `• ${category.toUpperCase()} ${subcat}: ${sublang.join(', ')}`
        ).join('\n');
    } else {
        return `• ${category.toUpperCase()}: ${langs.join(', ')}`;
    }
}).join('\n')}

🛠️ **Frameworks & Tools:**
${Object.entries(this.frameworks).map(([category, frameworks]) => 
    `• ${category.toUpperCase()}: ${frameworks.join(', ')}`
).join('\n')}

🗄️ **Database Systems:**
${Object.entries(this.databases).map(([type, dbs]) => 
    `• ${type.toUpperCase()}: ${dbs.join(', ')}`
).join('\n')}

☁️ **Cloud Platforms:**
${Object.entries(this.cloudPlatforms).map(([provider, services]) => 
    `• ${provider.toUpperCase()}: ${services.join(', ')}`
).join('\n')}

---

🏗️ **Your Approach:**
1. **Requirements Analysis**: Understand the complete project scope and requirements
2. **Technology Selection**: Choose the optimal tech stack for the specific use case
3. **Architecture Design**: Create scalable, maintainable system architecture
4. **Implementation**: Write clean, efficient, and well-documented code
5. **Testing**: Implement comprehensive testing strategies
6. **Deployment**: Set up production-ready deployment pipelines
7. **Documentation**: Provide complete project documentation

---

🚀 **Project Types You Can Create:**
- **Web Applications**: Full-stack web apps with modern frameworks
- **Mobile Apps**: Native and cross-platform mobile applications
- **Desktop Applications**: Cross-platform desktop software
- **APIs & Microservices**: RESTful APIs, GraphQL, gRPC services
- **Data Processing**: ETL pipelines, data analysis, ML models
- **Game Development**: 2D/3D games with popular engines
- **System Tools**: CLI tools, automation scripts, system utilities
- **IoT Applications**: Embedded systems and IoT device software

---

💡 **Best Practices:**
- Follow language-specific conventions and best practices
- Implement proper error handling and logging
- Write comprehensive tests (unit, integration, E2E)
- Use version control with meaningful commit messages
- Implement security best practices
- Optimize for performance and scalability
- Create clear documentation and README files
- Set up CI/CD pipelines for automated deployment

Always deliver production-ready, well-architected solutions with complete project setup.
        `;
    }
    
    async executeTask(task) {
        const result = await super.executeTask(task);
        
        try {
            const taskType = this.analyzeTaskType(task.query);
            
            switch (taskType) {
                case 'full_project':
                    return await this.handleFullProjectCreation(task);
                case 'web_application':
                    return await this.handleWebApplication(task);
                case 'mobile_application':
                    return await this.handleMobileApplication(task);
                case 'desktop_application':
                    return await this.handleDesktopApplication(task);
                case 'api_development':
                    return await this.handleApiDevelopment(task);
                case 'data_processing':
                    return await this.handleDataProcessing(task);
                case 'game_development':
                    return await this.handleGameDevelopment(task);
                case 'system_tool':
                    return await this.handleSystemTool(task);
                case 'code_migration':
                    return await this.handleCodeMigration(task);
                default:
                    return await this.handleGeneralCodingTask(task);
            }
        } catch (error) {
            this.logger.error('Universal Coder Agent task execution failed:', error);
            throw error;
        }
    }
    
    analyzeTaskType(query) {
        const lowerQuery = query.toLowerCase();
        
        if (lowerQuery.includes('create project') || lowerQuery.includes('build application') || lowerQuery.includes('full project')) {
            return 'full_project';
        } else if (lowerQuery.includes('web app') || lowerQuery.includes('website') || lowerQuery.includes('web application')) {
            return 'web_application';
        } else if (lowerQuery.includes('mobile app') || lowerQuery.includes('android') || lowerQuery.includes('ios')) {
            return 'mobile_application';
        } else if (lowerQuery.includes('desktop app') || lowerQuery.includes('desktop application') || lowerQuery.includes('gui')) {
            return 'desktop_application';
        } else if (lowerQuery.includes('api') || lowerQuery.includes('microservice') || lowerQuery.includes('backend service')) {
            return 'api_development';
        } else if (lowerQuery.includes('data') || lowerQuery.includes('etl') || lowerQuery.includes('machine learning')) {
            return 'data_processing';
        } else if (lowerQuery.includes('game') || lowerQuery.includes('unity') || lowerQuery.includes('unreal')) {
            return 'game_development';
        } else if (lowerQuery.includes('cli') || lowerQuery.includes('script') || lowerQuery.includes('tool')) {
            return 'system_tool';
        } else if (lowerQuery.includes('migrate') || lowerQuery.includes('convert') || lowerQuery.includes('refactor')) {
            return 'code_migration';
        }
        
        return 'general';
    }
    
    async handleFullProjectCreation(task) {
        const prompt = `
As a Universal Programming Expert, help create a complete project from scratch:

Task: ${task.query}

Please provide:
1. **Project Analysis & Requirements**
   - Functional and non-functional requirements
   - Target audience and use cases
   - Success criteria and constraints

2. **Technology Stack Selection**
   - Programming languages and frameworks
   - Database and storage solutions
   - Third-party services and APIs
   - Development and deployment tools

3. **System Architecture**
   - High-level architecture diagram
   - Component breakdown and responsibilities
   - Data flow and communication patterns
   - Scalability and performance considerations

4. **Implementation Plan**
   - Project structure and file organization
   - Development phases and milestones
   - Core features implementation
   - Testing strategy

5. **Complete Code Implementation**
   - All necessary files and configurations
   - Database schemas and migrations
   - API endpoints and business logic
   - Frontend components and styling
   - Testing suites

6. **Deployment & DevOps**
   - Environment configuration
   - CI/CD pipeline setup
   - Monitoring and logging
   - Security considerations

7. **Documentation**
   - README with setup instructions
   - API documentation
   - User guides
   - Developer documentation

Focus on creating a production-ready, scalable, and maintainable solution.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleWebApplication(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['web', 'frontend', 'backend']);
        
        const prompt = `
As a Full-Stack Web Developer, help create a complete web application:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Frontend implementation with modern framework
2. Backend API with proper architecture
3. Database design and integration
4. Authentication and authorization
5. Responsive design and accessibility
6. Performance optimization
7. Testing implementation
8. Deployment configuration

Focus on modern web standards and best practices.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleMobileApplication(task) {
        const prompt = `
As a Mobile Development Expert, help create a mobile application:

Task: ${task.query}

Please provide:
1. Platform selection (native vs cross-platform)
2. UI/UX design for mobile interfaces
3. Navigation and state management
4. Device feature integration
5. Offline functionality and data sync
6. Performance optimization
7. Testing on multiple devices
8. App store deployment

Focus on mobile-first design and platform-specific guidelines.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleDesktopApplication(task) {
        const prompt = `
As a Desktop Application Developer, help create a desktop application:

Task: ${task.query}

Please provide:
1. Framework selection (native vs cross-platform)
2. GUI design and user experience
3. File system and OS integration
4. Performance and memory optimization
5. Cross-platform compatibility
6. Installation and distribution
7. Auto-update mechanisms
8. Testing across operating systems

Focus on desktop-specific patterns and OS integration.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleApiDevelopment(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['api', 'backend', 'service']);
        
        const prompt = `
As an API Development Expert, help create robust APIs:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. API design and specification (OpenAPI/Swagger)
2. Endpoint implementation with proper HTTP methods
3. Request/response validation and serialization
4. Authentication and authorization
5. Error handling and status codes
6. Rate limiting and security measures
7. Documentation and testing
8. Monitoring and logging

Focus on RESTful principles, GraphQL, or gRPC as appropriate.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleDataProcessing(task) {
        const prompt = `
As a Data Processing Expert, help create data solutions:

Task: ${task.query}

Please provide:
1. Data pipeline architecture
2. ETL/ELT process implementation
3. Data validation and cleaning
4. Storage and retrieval optimization
5. Analytics and reporting
6. Machine learning integration
7. Monitoring and alerting
8. Scalability considerations

Focus on data quality, performance, and reliability.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGameDevelopment(task) {
        const prompt = `
As a Game Development Expert, help create games:

Task: ${task.query}

Please provide:
1. Game engine selection and setup
2. Game mechanics and systems design
3. Asset management and optimization
4. Physics and collision detection
5. Audio and visual effects
6. User interface and menus
7. Performance optimization
8. Platform-specific considerations

Focus on engaging gameplay and smooth performance.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleSystemTool(task) {
        const prompt = `
As a System Programming Expert, help create system tools:

Task: ${task.query}

Please provide:
1. Command-line interface design
2. System integration and APIs
3. File and process management
4. Error handling and logging
5. Configuration management
6. Cross-platform compatibility
7. Installation and packaging
8. Documentation and usage examples

Focus on reliability, performance, and usability.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleCodeMigration(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Code Migration Expert, help migrate or refactor code:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Migration strategy and planning
2. Code analysis and dependency mapping
3. Step-by-step migration process
4. Testing and validation approach
5. Risk mitigation strategies
6. Performance comparison
7. Documentation updates
8. Training and knowledge transfer

Focus on maintaining functionality while improving code quality.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGeneralCodingTask(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Universal Programming Expert, help with this coding task:

Task: ${task.query}

Context from codebase:
${context}

Please provide a comprehensive solution with proper architecture, implementation, testing, and documentation.
        `;
        
        return await this.generateResponse(prompt, task);
    }
}
