#!/usr/bin/env node

/**
 * SHAI AI Assistant - Main Entry Point
 * Advanced AI Coding Assistant powered by Gemini 2.0 Flash
 * 
 * Features:
 * - Multi-agent architecture with specialized sub-agents
 * - Real-time codebase analysis and context understanding
 * - CLI and Web UI interfaces
 * - Production-ready with comprehensive logging and error handling
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import chalk from 'chalk';
import figlet from 'figlet';
import gradient from 'gradient-string';
import { Orchestrator } from './core/orchestrator.js';
import { Logger } from './utils/logger.js';
import { ConfigManager } from './utils/config.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class ShaiAssistant {
    constructor() {
        this.logger = new Logger();
        this.config = new ConfigManager();
        this.orchestrator = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            console.log('🔄 Initializing SHAI AI Assistant...');

            // Display welcome banner
            this.displayBanner();

            console.log('📋 Loading configuration...');
            // Initialize configuration
            await this.config.initialize();
            console.log('✅ Configuration loaded successfully');

            console.log('🤖 Initializing orchestrator...');
            // Initialize orchestrator
            this.orchestrator = new Orchestrator(this.config, this.logger);
            await this.orchestrator.initialize();
            console.log('✅ Orchestrator initialized successfully');

            this.isInitialized = true;
            this.logger.info('SHAI AI Assistant initialized successfully');
            console.log('🎉 SHAI AI Assistant ready!');

            return true;
        } catch (error) {
            console.error('❌ Failed to initialize SHAI AI Assistant:', error);
            this.logger.error('Failed to initialize SHAI AI Assistant:', error);
            return false;
        }
    }

    displayBanner() {
        console.clear();
        
        const banner = figlet.textSync('SHAI AI', {
            font: 'ANSI Shadow',
            horizontalLayout: 'default',
            verticalLayout: 'default'
        });
        
        const gradientBanner = gradient(['#00f5ff', '#0080ff', '#0040ff'])(banner);
        console.log(gradientBanner);
        
        console.log(chalk.cyan('\n🚀 Advanced AI Coding Assistant powered by Gemini 2.0 Flash'));
        console.log(chalk.gray('━'.repeat(80)));
        console.log(chalk.yellow('✨ Multi-agent architecture with specialized sub-agents'));
        console.log(chalk.yellow('🧠 Real-time codebase analysis and context understanding'));
        console.log(chalk.yellow('💻 CLI and Web UI interfaces'));
        console.log(chalk.yellow('🔒 Production-ready with comprehensive security'));
        console.log(chalk.gray('━'.repeat(80)));
    }

    async start() {
        try {
            console.log('🚀 Starting SHAI AI Assistant...');

            if (!this.isInitialized) {
                console.log('🔄 Initializing components...');
                const initialized = await this.initialize();
                if (!initialized) {
                    console.error('❌ Initialization failed');
                    process.exit(1);
                }
            }

            // Parse command line arguments
            const args = process.argv.slice(2);
            const mode = args[0] || 'interactive';

            console.log(`🎯 Starting in ${mode} mode...`);

            switch (mode) {
                case 'cli':
                    await this.startCLI();
                    break;
                case 'web':
                    await this.startWebServer();
                    break;
                case 'interactive':
                    await this.startInteractive();
                    break;
                case 'daemon':
                    await this.startDaemon();
                    break;
                default:
                    this.showUsage();
            }
        } catch (error) {
            console.error('❌ Failed to start SHAI AI Assistant:', error);
            this.logger?.error('Failed to start SHAI AI Assistant:', error);
            process.exit(1);
        }
    }

    async startCLI() {
        this.logger.info('Starting CLI mode');
        const { CLIInterface } = await import('./ui/cli/index.js');
        const cli = new CLIInterface(this.orchestrator, this.logger);
        await cli.start();
    }

    async startWebServer() {
        this.logger.info('Starting Web Server mode');
        const { WebServer } = await import('./ui/web/server.js');
        const webServer = new WebServer(this.orchestrator, this.config, this.logger);
        await webServer.start();
    }

    async startInteractive() {
        try {
            this.logger.info('Starting Interactive mode');
            console.log(chalk.green('\n🎯 SHAI AI Assistant is ready!'));
            console.log(chalk.cyan('Choose your interface:'));
            console.log(chalk.white('1. CLI Interface (Command Line)'));
            console.log(chalk.white('2. Web Interface (Browser)'));
            console.log(chalk.white('3. Both (CLI + Web)'));

            const { default: inquirer } = await import('inquirer');
            const { interface: selectedInterface } = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'interface',
                    message: 'Select interface:',
                    choices: [
                        { name: 'CLI Interface', value: 'cli' },
                        { name: 'Web Interface', value: 'web' },
                        { name: 'Both (CLI + Web)', value: 'both' }
                    ]
                }
            ]);

            if (selectedInterface === 'both') {
                // Start web server in background
                console.log('🌐 Starting web server...');
                this.startWebServer();
                // Start CLI in foreground
                console.log('💻 Starting CLI interface...');
                await this.startCLI();
            } else if (selectedInterface === 'web') {
                await this.startWebServer();
            } else {
                await this.startCLI();
            }
        } catch (error) {
            console.error('❌ Failed to start interactive mode:', error);
            this.logger.error('Failed to start interactive mode:', error);
            throw error;
        }
    }

    async startDaemon() {
        this.logger.info('Starting Daemon mode');
        // Start web server and keep running
        await this.startWebServer();
    }

    showUsage() {
        console.log(chalk.cyan('\nUsage: node src/index.js [mode]'));
        console.log(chalk.white('\nModes:'));
        console.log(chalk.yellow('  interactive  ') + chalk.gray('(default) - Interactive mode with interface selection'));
        console.log(chalk.yellow('  cli          ') + chalk.gray('- Command Line Interface'));
        console.log(chalk.yellow('  web          ') + chalk.gray('- Web Server Interface'));
        console.log(chalk.yellow('  daemon       ') + chalk.gray('- Background daemon mode'));
        console.log(chalk.white('\nExamples:'));
        console.log(chalk.gray('  node src/index.js'));
        console.log(chalk.gray('  node src/index.js cli'));
        console.log(chalk.gray('  node src/index.js web'));
        console.log(chalk.gray('  npm start'));
        console.log(chalk.gray('  npm run cli'));
        console.log(chalk.gray('  npm run web'));
    }

    async shutdown() {
        this.logger.info('Shutting down SHAI AI Assistant');
        
        if (this.orchestrator) {
            await this.orchestrator.shutdown();
        }
        
        console.log(chalk.green('\n👋 SHAI AI Assistant shutdown complete'));
        process.exit(0);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log(chalk.yellow('\n\n🛑 Received SIGINT, shutting down gracefully...'));
    if (global.shaiAssistant) {
        await global.shaiAssistant.shutdown();
    } else {
        process.exit(0);
    }
});

process.on('SIGTERM', async () => {
    console.log(chalk.yellow('\n\n🛑 Received SIGTERM, shutting down gracefully...'));
    if (global.shaiAssistant) {
        await global.shaiAssistant.shutdown();
    } else {
        process.exit(0);
    }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(chalk.red('💥 Uncaught Exception:'), error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk.red('💥 Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
    process.exit(1);
});

// Start the application
async function main() {
    const assistant = new ShaiAssistant();
    global.shaiAssistant = assistant;
    await assistant.start();
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error(chalk.red('💥 Failed to start SHAI AI Assistant:'), error);
        process.exit(1);
    });
}

export { ShaiAssistant };
