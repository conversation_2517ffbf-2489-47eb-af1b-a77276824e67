{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.10", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "contributors": ["<PERSON>", "<PERSON>"], "files": ["index.js", "lib/"], "license": "MIT", "repository": "nodeca/argparse", "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "^2.13.1", "istanbul": "^0.4.5", "mocha": "^3.1.0", "ndoc": "^5.0.1"}}