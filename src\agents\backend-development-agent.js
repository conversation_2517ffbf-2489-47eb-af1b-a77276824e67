/**
 * Backend Development Agent - Specialized in API development, database design, server architecture
 */

import { BaseAgent } from './base-agent.js';

export class BackendDevelopmentAgent extends BaseAgent {
    constructor(orchestrator, logger) {
        super(orchestrator, logger);
        this.name = 'BackendDevelopmentAgent';
        this.specialization = 'Backend Development';
        this.capabilities = [
            'API Development & Design',
            'Database Schema Design & Optimization',
            'Server Architecture & Scalability',
            'Microservices Architecture',
            'Authentication & Authorization',
            'Caching Strategies',
            'Message Queues & Event Streaming',
            'Performance Optimization',
            'Security Implementation',
            'DevOps Integration',
            'Testing Strategies',
            'Documentation Generation'
        ];
        
        this.supportedFrameworks = {
            nodejs: ['Express.js', 'Fastify', 'Koa.js', 'NestJS', 'Hapi.js'],
            python: ['Django', 'FastAPI', 'Flask', 'Tornado', 'Pyramid'],
            java: ['Spring Boot', 'Quarkus', 'Micronaut', 'Dropwizard'],
            csharp: ['ASP.NET Core', '.NET 8', 'Minimal APIs'],
            go: ['Gin', 'Echo', 'Fiber', 'Chi', 'Gorilla Mux'],
            rust: ['Actix-web', 'Warp', 'Rocket', 'Axum'],
            php: ['Laravel', 'Symfony', 'CodeIgniter', 'Slim'],
            ruby: ['Ruby on Rails', 'Sinatra', 'Hanami'],
            kotlin: ['Ktor', 'Spring Boot with Kotlin']
        };
        
        this.databases = {
            relational: ['PostgreSQL', 'MySQL', 'SQLite', 'SQL Server', 'Oracle'],
            nosql: ['MongoDB', 'Redis', 'Cassandra', 'DynamoDB', 'CouchDB'],
            graph: ['Neo4j', 'ArangoDB', 'Amazon Neptune'],
            timeseries: ['InfluxDB', 'TimescaleDB', 'Prometheus'],
            search: ['Elasticsearch', 'Solr', 'Algolia']
        };
        
        this.cloudServices = {
            aws: ['Lambda', 'API Gateway', 'RDS', 'DynamoDB', 'S3', 'SQS', 'SNS'],
            azure: ['Functions', 'App Service', 'SQL Database', 'Cosmos DB', 'Service Bus'],
            gcp: ['Cloud Functions', 'App Engine', 'Cloud SQL', 'Firestore', 'Pub/Sub'],
            general: ['Docker', 'Kubernetes', 'Terraform', 'Ansible']
        };
    }
    
    getSystemPrompt() {
        return `
You are a **Senior elite Backend Development Expert** with deep expertise in server-side architecture, API design, and scalable system development.

🎯 **Core Specializations:**
${this.capabilities.map(cap => `• ${cap}`).join('\n')}

🛠️ **Technology Stack Expertise:**
**Languages & Frameworks:**
${Object.entries(this.supportedFrameworks).map(([lang, frameworks]) => 
    `• ${lang.toUpperCase()}: ${frameworks.join(', ')}`
).join('\n')}

**Database Systems:**
${Object.entries(this.databases).map(([type, dbs]) => 
    `• ${type.toUpperCase()}: ${dbs.join(', ')}`
).join('\n')}

**Cloud & Infrastructure:**
${Object.entries(this.cloudServices).map(([provider, services]) => 
    `• ${provider.toUpperCase()}: ${services.join(', ')}`
).join('\n')}

---

🏗️ **Your Approach:**
1. **Architecture First**: Always consider scalability, maintainability, and performance
2. **Security by Design**: Implement authentication, authorization, and data protection
3. **API Excellence**: Design RESTful APIs, GraphQL schemas, or gRPC services
4. **Database Optimization**: Choose the right database and optimize queries
5. **Testing Strategy**: Unit tests, integration tests, and load testing
6. **Documentation**: Comprehensive API documentation and system architecture

---

🚀 **Deliverables:**
- Complete backend applications with proper structure
- Database schemas with migrations and seeders
- API endpoints with validation and error handling
- Authentication and authorization systems
- Caching and performance optimizations
- Docker configurations and deployment scripts
- Comprehensive testing suites
- API documentation (OpenAPI/Swagger)

---

💡 **Best Practices:**
- Follow SOLID principles and clean architecture
- Implement proper error handling and logging
- Use environment-based configuration
- Apply security best practices (OWASP guidelines)
- Optimize for performance and scalability
- Ensure proper monitoring and observability

Always provide production-ready, secure, and scalable backend solutions.
        `;
    }
    
    async executeTask(task) {
        const result = await super.executeTask(task);
        
        try {
            // Analyze the task type and provide specialized backend development assistance
            const taskType = this.analyzeTaskType(task.query);
            
            switch (taskType) {
                case 'api_development':
                    return await this.handleApiDevelopment(task);
                case 'database_design':
                    return await this.handleDatabaseDesign(task);
                case 'architecture_design':
                    return await this.handleArchitectureDesign(task);
                case 'performance_optimization':
                    return await this.handlePerformanceOptimization(task);
                case 'security_implementation':
                    return await this.handleSecurityImplementation(task);
                case 'microservices':
                    return await this.handleMicroservicesDesign(task);
                default:
                    return await this.handleGeneralBackendTask(task);
            }
        } catch (error) {
            this.logger.error('Backend Development Agent task execution failed:', error);
            throw error;
        }
    }
    
    analyzeTaskType(query) {
        const lowerQuery = query.toLowerCase();
        
        if (lowerQuery.includes('api') || lowerQuery.includes('endpoint') || lowerQuery.includes('rest')) {
            return 'api_development';
        } else if (lowerQuery.includes('database') || lowerQuery.includes('schema') || lowerQuery.includes('migration')) {
            return 'database_design';
        } else if (lowerQuery.includes('architecture') || lowerQuery.includes('system design') || lowerQuery.includes('scalability')) {
            return 'architecture_design';
        } else if (lowerQuery.includes('performance') || lowerQuery.includes('optimization') || lowerQuery.includes('caching')) {
            return 'performance_optimization';
        } else if (lowerQuery.includes('security') || lowerQuery.includes('authentication') || lowerQuery.includes('authorization')) {
            return 'security_implementation';
        } else if (lowerQuery.includes('microservice') || lowerQuery.includes('distributed') || lowerQuery.includes('service mesh')) {
            return 'microservices';
        }
        
        return 'general';
    }
    
    async handleApiDevelopment(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['api', 'routes', 'controllers']);
        
        const prompt = `
As a Backend Development Expert, help with API development:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. API endpoint design with proper HTTP methods
2. Request/response schemas with validation
3. Error handling and status codes
4. Authentication/authorization if needed
5. Database integration
6. Testing examples
7. API documentation

Focus on RESTful principles, security, and scalability.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleDatabaseDesign(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['models', 'schema', 'database']);
        
        const prompt = `
As a Database Design Expert, help with database architecture:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Database schema design with relationships
2. Indexing strategy for performance
3. Migration scripts
4. Data validation and constraints
5. Query optimization recommendations
6. Backup and recovery considerations
7. Scaling strategies

Consider normalization, performance, and data integrity.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleArchitectureDesign(task) {
        const prompt = `
As a System Architecture Expert, help with backend architecture:

Task: ${task.query}

Please provide:
1. High-level system architecture diagram
2. Component breakdown and responsibilities
3. Data flow and communication patterns
4. Scalability and performance considerations
5. Technology stack recommendations
6. Deployment architecture
7. Monitoring and observability strategy

Focus on scalability, maintainability, and reliability.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handlePerformanceOptimization(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['performance', 'optimization', 'caching']);
        
        const prompt = `
As a Performance Optimization Expert, help improve backend performance:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Performance bottleneck analysis
2. Database query optimization
3. Caching strategies (Redis, Memcached)
4. Load balancing recommendations
5. Code optimization techniques
6. Monitoring and profiling setup
7. Scalability improvements

Focus on measurable performance improvements.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleSecurityImplementation(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query, ['auth', 'security', 'middleware']);
        
        const prompt = `
As a Backend Security Expert, help implement security measures:

Task: ${task.query}

Context from codebase:
${context}

Please provide:
1. Authentication system implementation
2. Authorization and role-based access control
3. Input validation and sanitization
4. Security headers and middleware
5. Rate limiting and DDoS protection
6. Data encryption and secure storage
7. Security testing recommendations

Follow OWASP security guidelines and best practices.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleMicroservicesDesign(task) {
        const prompt = `
As a Microservices Architecture Expert, help design distributed systems:

Task: ${task.query}

Please provide:
1. Service decomposition strategy
2. Inter-service communication patterns
3. Data management in distributed systems
4. Service discovery and load balancing
5. Fault tolerance and circuit breakers
6. Monitoring and distributed tracing
7. Deployment and orchestration

Focus on service boundaries, data consistency, and operational complexity.
        `;
        
        return await this.generateResponse(prompt, task);
    }
    
    async handleGeneralBackendTask(task) {
        const context = await this.orchestrator.contextEngine.getRelevantContext(task.query);
        
        const prompt = `
As a Senior Backend Developer, help with this backend development task:

Task: ${task.query}

Context from codebase:
${context}

Please provide a comprehensive solution following backend development best practices.
        `;
        
        return await this.generateResponse(prompt, task);
    }
}
