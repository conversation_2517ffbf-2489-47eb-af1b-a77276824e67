/**
 * Code Quality Linter - Comprehensive linting and automatic fixing for all supported languages
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { EventEmitter } from 'events';

export class CodeQualityLinter extends EventEmitter {
    constructor(logger, projectRoot = process.cwd()) {
        super();
        this.logger = logger.child('CodeQualityLinter');
        this.projectRoot = projectRoot;
        
        this.linters = {
            javascript: {
                eslint: {
                    command: 'eslint',
                    args: ['--format', 'json'],
                    fixArgs: ['--fix'],
                    configFiles: ['.eslintrc.js', '.eslintrc.json', '.eslintrc.yml', 'eslint.config.js'],
                    extensions: ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs']
                },
                prettier: {
                    command: 'prettier',
                    args: ['--check'],
                    fixArgs: ['--write'],
                    configFiles: ['.prettierrc', '.prettierrc.json', '.prettierrc.yml', 'prettier.config.js'],
                    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.css', '.scss', '.html']
                },
                jshint: {
                    command: 'jshint',
                    args: ['--reporter', 'json'],
                    configFiles: ['.jshintrc'],
                    extensions: ['.js']
                }
            },
            
            typescript: {
                tslint: {
                    command: 'tslint',
                    args: ['--format', 'json'],
                    fixArgs: ['--fix'],
                    configFiles: ['tslint.json'],
                    extensions: ['.ts', '.tsx']
                },
                tsc: {
                    command: 'tsc',
                    args: ['--noEmit', '--pretty', 'false'],
                    configFiles: ['tsconfig.json'],
                    extensions: ['.ts', '.tsx']
                }
            },
            
            python: {
                pylint: {
                    command: 'pylint',
                    args: ['--output-format=json'],
                    configFiles: ['.pylintrc', 'pylint.cfg', 'pyproject.toml'],
                    extensions: ['.py']
                },
                flake8: {
                    command: 'flake8',
                    args: ['--format=json'],
                    configFiles: ['.flake8', 'setup.cfg', 'tox.ini'],
                    extensions: ['.py']
                },
                black: {
                    command: 'black',
                    args: ['--check', '--diff'],
                    fixArgs: [],
                    configFiles: ['pyproject.toml'],
                    extensions: ['.py']
                },
                isort: {
                    command: 'isort',
                    args: ['--check-only', '--diff'],
                    fixArgs: [],
                    configFiles: ['.isort.cfg', 'pyproject.toml', 'setup.cfg'],
                    extensions: ['.py']
                },
                mypy: {
                    command: 'mypy',
                    args: ['--show-error-codes', '--no-error-summary'],
                    configFiles: ['mypy.ini', '.mypy.ini', 'pyproject.toml', 'setup.cfg'],
                    extensions: ['.py']
                }
            },
            
            rust: {
                clippy: {
                    command: 'cargo',
                    args: ['clippy', '--message-format=json'],
                    fixArgs: ['clippy', '--fix', '--allow-dirty'],
                    configFiles: ['Cargo.toml'],
                    extensions: ['.rs']
                },
                rustfmt: {
                    command: 'cargo',
                    args: ['fmt', '--check'],
                    fixArgs: ['fmt'],
                    configFiles: ['rustfmt.toml', '.rustfmt.toml'],
                    extensions: ['.rs']
                }
            },
            
            go: {
                golint: {
                    command: 'golint',
                    args: [],
                    extensions: ['.go']
                },
                gofmt: {
                    command: 'gofmt',
                    args: ['-l'],
                    fixArgs: ['-w'],
                    extensions: ['.go']
                },
                govet: {
                    command: 'go',
                    args: ['vet'],
                    extensions: ['.go']
                },
                staticcheck: {
                    command: 'staticcheck',
                    args: [],
                    extensions: ['.go']
                }
            },
            
            php: {
                phpcs: {
                    command: 'phpcs',
                    args: ['--report=json'],
                    fixArgs: ['--fix'],
                    configFiles: ['phpcs.xml', '.phpcs.xml', 'phpcs.xml.dist'],
                    extensions: ['.php']
                },
                phpstan: {
                    command: 'phpstan',
                    args: ['analyse', '--error-format=json'],
                    configFiles: ['phpstan.neon', 'phpstan.neon.dist'],
                    extensions: ['.php']
                },
                psalm: {
                    command: 'psalm',
                    args: ['--output-format=json'],
                    configFiles: ['psalm.xml'],
                    extensions: ['.php']
                }
            },
            
            ruby: {
                rubocop: {
                    command: 'rubocop',
                    args: ['--format', 'json'],
                    fixArgs: ['--auto-correct'],
                    configFiles: ['.rubocop.yml'],
                    extensions: ['.rb']
                },
                reek: {
                    command: 'reek',
                    args: ['--format', 'json'],
                    configFiles: ['.reek.yml'],
                    extensions: ['.rb']
                }
            },
            
            java: {
                checkstyle: {
                    command: 'checkstyle',
                    args: ['-f', 'json'],
                    configFiles: ['checkstyle.xml'],
                    extensions: ['.java']
                },
                spotbugs: {
                    command: 'spotbugs',
                    args: ['-textui', '-xml'],
                    extensions: ['.java']
                }
            },
            
            csharp: {
                dotnetformat: {
                    command: 'dotnet',
                    args: ['format', '--verify-no-changes'],
                    fixArgs: ['format'],
                    extensions: ['.cs']
                }
            },
            
            css: {
                stylelint: {
                    command: 'stylelint',
                    args: ['--formatter', 'json'],
                    fixArgs: ['--fix'],
                    configFiles: ['.stylelintrc', '.stylelintrc.json', '.stylelintrc.yml', 'stylelint.config.js'],
                    extensions: ['.css', '.scss', '.sass', '.less']
                }
            },
            
            html: {
                htmlhint: {
                    command: 'htmlhint',
                    args: ['--format', 'json'],
                    configFiles: ['.htmlhintrc'],
                    extensions: ['.html', '.htm']
                }
            },
            
            yaml: {
                yamllint: {
                    command: 'yamllint',
                    args: ['-f', 'parsable'],
                    configFiles: ['.yamllint', '.yamllint.yml'],
                    extensions: ['.yml', '.yaml']
                }
            },
            
            json: {
                jsonlint: {
                    command: 'jsonlint',
                    args: [],
                    extensions: ['.json']
                }
            },
            
            markdown: {
                markdownlint: {
                    command: 'markdownlint',
                    args: ['--json'],
                    fixArgs: ['--fix'],
                    configFiles: ['.markdownlint.json', '.markdownlint.yml'],
                    extensions: ['.md', '.markdown']
                }
            },
            
            dockerfile: {
                hadolint: {
                    command: 'hadolint',
                    args: ['--format', 'json'],
                    extensions: ['Dockerfile', 'dockerfile']
                }
            }
        };
        
        this.severityLevels = {
            error: 3,
            warning: 2,
            info: 1,
            style: 0
        };
    }
    
    async detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const basename = path.basename(filePath).toLowerCase();
        
        // Special cases
        if (basename === 'dockerfile' || basename.startsWith('dockerfile.')) {
            return 'dockerfile';
        }
        
        // Extension-based detection
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.mjs': 'javascript',
            '.cjs': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.rs': 'rust',
            '.go': 'go',
            '.php': 'php',
            '.rb': 'ruby',
            '.java': 'java',
            '.cs': 'csharp',
            '.css': 'css',
            '.scss': 'css',
            '.sass': 'css',
            '.less': 'css',
            '.html': 'html',
            '.htm': 'html',
            '.yml': 'yaml',
            '.yaml': 'yaml',
            '.json': 'json',
            '.md': 'markdown',
            '.markdown': 'markdown'
        };
        
        return languageMap[ext] || null;
    }
    
    async isLinterAvailable(linterName, command) {
        try {
            execSync(`${command} --version`, { 
                stdio: 'ignore',
                timeout: 5000
            });
            return true;
        } catch (error) {
            this.logger.debug(`Linter ${linterName} not available: ${command}`);
            return false;
        }
    }
    
    async getAvailableLinters(language) {
        const languageLinters = this.linters[language];
        if (!languageLinters) return [];
        
        const available = [];
        
        for (const [name, config] of Object.entries(languageLinters)) {
            const isAvailable = await this.isLinterAvailable(name, config.command);
            if (isAvailable) {
                available.push({ name, config });
            }
        }
        
        return available;
    }
    
    async lintFile(filePath, options = {}) {
        const {
            fix = false,
            linters = null, // null means use all available
            severity = 'info'
        } = options;
        
        try {
            const language = await this.detectLanguage(filePath);
            if (!language) {
                return {
                    success: false,
                    error: 'Unsupported file type',
                    filePath
                };
            }
            
            const availableLinters = await this.getAvailableLinters(language);
            if (availableLinters.length === 0) {
                return {
                    success: false,
                    error: `No linters available for ${language}`,
                    filePath
                };
            }
            
            const results = [];
            const selectedLinters = linters ? 
                availableLinters.filter(l => linters.includes(l.name)) : 
                availableLinters;
            
            for (const { name, config } of selectedLinters) {
                try {
                    const result = await this.runLinter(name, config, filePath, fix);
                    results.push({
                        linter: name,
                        ...result
                    });
                } catch (error) {
                    results.push({
                        linter: name,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            // Combine and filter results
            const allIssues = results
                .filter(r => r.success && r.issues)
                .flatMap(r => r.issues.map(issue => ({ ...issue, linter: r.linter })));
            
            const filteredIssues = this.filterBySeverity(allIssues, severity);
            
            return {
                success: true,
                filePath,
                language,
                issues: filteredIssues,
                linterResults: results,
                summary: this.generateSummary(filteredIssues)
            };
            
        } catch (error) {
            this.logger.error(`Failed to lint file ${filePath}:`, error);
            return {
                success: false,
                error: error.message,
                filePath
            };
        }
    }
    
    async runLinter(linterName, config, filePath, fix = false) {
        return new Promise((resolve, reject) => {
            const args = fix && config.fixArgs ? 
                [...config.fixArgs, filePath] : 
                [...config.args, filePath];
            
            const process = spawn(config.command, args, {
                cwd: this.projectRoot,
                stdio: ['ignore', 'pipe', 'pipe']
            });
            
            let stdout = '';
            let stderr = '';
            
            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            process.on('close', (code) => {
                try {
                    const result = this.parseLinterOutput(linterName, stdout, stderr, code);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
            
            process.on('error', (error) => {
                reject(error);
            });
        });
    }
    
    parseLinterOutput(linterName, stdout, stderr, exitCode) {
        const issues = [];
        
        try {
            switch (linterName) {
                case 'eslint':
                    if (stdout.trim()) {
                        const results = JSON.parse(stdout);
                        for (const file of results) {
                            for (const message of file.messages) {
                                issues.push({
                                    line: message.line,
                                    column: message.column,
                                    severity: message.severity === 2 ? 'error' : 'warning',
                                    message: message.message,
                                    rule: message.ruleId,
                                    fixable: message.fix !== undefined
                                });
                            }
                        }
                    }
                    break;
                    
                case 'pylint':
                    if (stdout.trim()) {
                        const results = JSON.parse(stdout);
                        for (const issue of results) {
                            issues.push({
                                line: issue.line,
                                column: issue.column,
                                severity: this.mapPylintSeverity(issue.type),
                                message: issue.message,
                                rule: issue['message-id'],
                                fixable: false
                            });
                        }
                    }
                    break;
                    
                case 'clippy':
                    const lines = stdout.split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        try {
                            const result = JSON.parse(line);
                            if (result.message && result.message.spans) {
                                for (const span of result.message.spans) {
                                    issues.push({
                                        line: span.line_start,
                                        column: span.column_start,
                                        severity: result.message.level,
                                        message: result.message.message,
                                        rule: result.message.code?.code,
                                        fixable: false
                                    });
                                }
                            }
                        } catch (e) {
                            // Skip non-JSON lines
                        }
                    }
                    break;
                    
                default:
                    // Generic parsing for simple output
                    if (stderr && exitCode !== 0) {
                        issues.push({
                            line: 1,
                            column: 1,
                            severity: 'error',
                            message: stderr.trim(),
                            rule: linterName,
                            fixable: false
                        });
                    }
            }
            
            return {
                success: true,
                issues,
                output: stdout,
                error: stderr,
                exitCode
            };
            
        } catch (error) {
            return {
                success: false,
                error: `Failed to parse ${linterName} output: ${error.message}`,
                output: stdout,
                stderr
            };
        }
    }
    
    mapPylintSeverity(type) {
        const mapping = {
            'error': 'error',
            'warning': 'warning',
            'refactor': 'info',
            'convention': 'style',
            'info': 'info'
        };
        return mapping[type] || 'info';
    }
    
    filterBySeverity(issues, minSeverity) {
        const minLevel = this.severityLevels[minSeverity] || 0;
        return issues.filter(issue => 
            (this.severityLevels[issue.severity] || 0) >= minLevel
        );
    }
    
    generateSummary(issues) {
        const summary = {
            total: issues.length,
            errors: 0,
            warnings: 0,
            info: 0,
            style: 0,
            fixable: 0
        };
        
        for (const issue of issues) {
            summary[issue.severity] = (summary[issue.severity] || 0) + 1;
            if (issue.fixable) {
                summary.fixable++;
            }
        }
        
        return summary;
    }
    
    async lintDirectory(dirPath, options = {}) {
        const {
            recursive = true,
            extensions = null,
            exclude = ['node_modules', '.git', 'dist', 'build'],
            ...lintOptions
        } = options;
        
        try {
            const files = await this.findLintableFiles(dirPath, {
                recursive,
                extensions,
                exclude
            });
            
            const results = [];
            const concurrency = 5; // Limit concurrent linting
            
            for (let i = 0; i < files.length; i += concurrency) {
                const batch = files.slice(i, i + concurrency);
                const batchResults = await Promise.all(
                    batch.map(file => this.lintFile(file, lintOptions))
                );
                results.push(...batchResults);
                
                // Emit progress
                this.emit('progress', {
                    completed: Math.min(i + concurrency, files.length),
                    total: files.length
                });
            }
            
            return {
                success: true,
                directory: dirPath,
                files: results,
                summary: this.generateDirectorySummary(results)
            };
            
        } catch (error) {
            this.logger.error(`Failed to lint directory ${dirPath}:`, error);
            return {
                success: false,
                error: error.message,
                directory: dirPath
            };
        }
    }
    
    async findLintableFiles(dirPath, options = {}) {
        const { recursive, extensions, exclude } = options;
        const files = [];
        
        const scan = async (currentPath, depth = 0) => {
            if (!recursive && depth > 0) return;
            
            try {
                const entries = await fs.readdir(currentPath, { withFileTypes: true });
                
                for (const entry of entries) {
                    const fullPath = path.join(currentPath, entry.name);
                    
                    if (exclude.includes(entry.name)) continue;
                    
                    if (entry.isDirectory()) {
                        await scan(fullPath, depth + 1);
                    } else if (entry.isFile()) {
                        const language = await this.detectLanguage(fullPath);
                        if (language && (!extensions || extensions.includes(path.extname(fullPath)))) {
                            files.push(fullPath);
                        }
                    }
                }
            } catch (error) {
                this.logger.warn(`Failed to scan directory ${currentPath}:`, error.message);
            }
        };
        
        await scan(dirPath);
        return files;
    }
    
    generateDirectorySummary(results) {
        const summary = {
            totalFiles: results.length,
            successfulFiles: results.filter(r => r.success).length,
            failedFiles: results.filter(r => !r.success).length,
            totalIssues: 0,
            errors: 0,
            warnings: 0,
            info: 0,
            style: 0,
            fixable: 0,
            languages: new Set(),
            linters: new Set()
        };
        
        for (const result of results) {
            if (result.success && result.issues) {
                summary.totalIssues += result.issues.length;
                summary.languages.add(result.language);
                
                for (const issue of result.issues) {
                    summary[issue.severity] = (summary[issue.severity] || 0) + 1;
                    if (issue.fixable) summary.fixable++;
                    if (issue.linter) summary.linters.add(issue.linter);
                }
            }
        }
        
        summary.languages = Array.from(summary.languages);
        summary.linters = Array.from(summary.linters);
        
        return summary;
    }
    
    async fixFile(filePath, options = {}) {
        return this.lintFile(filePath, { ...options, fix: true });
    }
    
    async fixDirectory(dirPath, options = {}) {
        return this.lintDirectory(dirPath, { ...options, fix: true });
    }
    
    getSupportedLanguages() {
        return Object.keys(this.linters);
    }
    
    getSupportedLinters(language = null) {
        if (language) {
            return Object.keys(this.linters[language] || {});
        }
        
        const allLinters = new Set();
        for (const languageLinters of Object.values(this.linters)) {
            for (const linter of Object.keys(languageLinters)) {
                allLinters.add(linter);
            }
        }
        
        return Array.from(allLinters);
    }
}
