/**
 * Multi-Language Package Manager - Support for all major package managers
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { EventEmitter } from 'events';

export class MultiLanguagePackageManager extends EventEmitter {
    constructor(logger, projectRoot = process.cwd()) {
        super();
        this.logger = logger.child('MultiPackageManager');
        this.projectRoot = projectRoot;
        this.detectedManagers = new Map();
        
        this.supportedLanguages = {
            javascript: {
                managers: ['npm', 'yarn', 'pnpm', 'bun'],
                files: ['package.json', 'yarn.lock', 'pnpm-lock.yaml', 'bun.lockb'],
                extensions: ['.js', '.ts', '.jsx', '.tsx', '.mjs', '.cjs']
            },
            python: {
                managers: ['pip', 'poetry', 'conda', 'pipenv', 'uv'],
                files: ['requirements.txt', 'pyproject.toml', 'environment.yml', 'Pipfile', 'setup.py'],
                extensions: ['.py', '.pyx', '.pyi']
            },
            rust: {
                managers: ['cargo'],
                files: ['Cargo.toml', 'Cargo.lock'],
                extensions: ['.rs']
            },
            go: {
                managers: ['go'],
                files: ['go.mod', 'go.sum'],
                extensions: ['.go']
            },
            php: {
                managers: ['composer'],
                files: ['composer.json', 'composer.lock'],
                extensions: ['.php', '.phtml']
            },
            ruby: {
                managers: ['gem', 'bundler'],
                files: ['Gemfile', 'Gemfile.lock', '*.gemspec'],
                extensions: ['.rb', '.rake']
            },
            java: {
                managers: ['maven', 'gradle', 'sbt'],
                files: ['pom.xml', 'build.gradle', 'build.sbt', 'gradle.properties'],
                extensions: ['.java', '.scala', '.kt']
            },
            csharp: {
                managers: ['nuget', 'dotnet', 'paket'],
                files: ['*.csproj', '*.sln', 'packages.config', 'paket.dependencies'],
                extensions: ['.cs', '.vb', '.fs']
            },
            swift: {
                managers: ['swift', 'cocoapods', 'carthage'],
                files: ['Package.swift', 'Podfile', 'Cartfile'],
                extensions: ['.swift']
            },
            dart: {
                managers: ['pub'],
                files: ['pubspec.yaml', 'pubspec.lock'],
                extensions: ['.dart']
            },
            cpp: {
                managers: ['conan', 'vcpkg', 'cmake'],
                files: ['conanfile.txt', 'vcpkg.json', 'CMakeLists.txt'],
                extensions: ['.cpp', '.cc', '.cxx', '.c', '.h', '.hpp']
            },
            kotlin: {
                managers: ['gradle', 'maven'],
                files: ['build.gradle.kts', 'pom.xml'],
                extensions: ['.kt', '.kts']
            },
            scala: {
                managers: ['sbt', 'mill'],
                files: ['build.sbt', 'build.sc'],
                extensions: ['.scala', '.sc']
            }
        };
        
        this.commands = {
            // JavaScript/Node.js
            npm: {
                install: 'npm install',
                add: 'npm install',
                remove: 'npm uninstall',
                update: 'npm update',
                list: 'npm list',
                outdated: 'npm outdated',
                audit: 'npm audit',
                clean: 'npm cache clean --force',
                init: 'npm init -y'
            },
            yarn: {
                install: 'yarn install',
                add: 'yarn add',
                remove: 'yarn remove',
                update: 'yarn upgrade',
                list: 'yarn list',
                outdated: 'yarn outdated',
                audit: 'yarn audit',
                clean: 'yarn cache clean',
                init: 'yarn init -y'
            },
            pnpm: {
                install: 'pnpm install',
                add: 'pnpm add',
                remove: 'pnpm remove',
                update: 'pnpm update',
                list: 'pnpm list',
                outdated: 'pnpm outdated',
                audit: 'pnpm audit',
                clean: 'pnpm store prune',
                init: 'pnpm init'
            },
            bun: {
                install: 'bun install',
                add: 'bun add',
                remove: 'bun remove',
                update: 'bun update',
                list: 'bun pm ls',
                outdated: 'bun outdated',
                audit: 'bun audit',
                clean: 'bun pm cache rm',
                init: 'bun init'
            },
            
            // Python
            pip: {
                install: 'pip install -r requirements.txt',
                add: 'pip install',
                remove: 'pip uninstall',
                update: 'pip install --upgrade',
                list: 'pip list',
                outdated: 'pip list --outdated',
                audit: 'pip-audit',
                clean: 'pip cache purge',
                init: 'pip freeze > requirements.txt'
            },
            poetry: {
                install: 'poetry install',
                add: 'poetry add',
                remove: 'poetry remove',
                update: 'poetry update',
                list: 'poetry show',
                outdated: 'poetry show --outdated',
                audit: 'poetry audit',
                clean: 'poetry cache clear --all pypi',
                init: 'poetry init'
            },
            conda: {
                install: 'conda env create -f environment.yml',
                add: 'conda install',
                remove: 'conda remove',
                update: 'conda update',
                list: 'conda list',
                outdated: 'conda search --outdated',
                audit: 'conda audit',
                clean: 'conda clean --all',
                init: 'conda env export > environment.yml'
            },
            pipenv: {
                install: 'pipenv install',
                add: 'pipenv install',
                remove: 'pipenv uninstall',
                update: 'pipenv update',
                list: 'pipenv graph',
                outdated: 'pipenv update --outdated',
                audit: 'pipenv check',
                clean: 'pipenv --clear',
                init: 'pipenv --python 3'
            },
            
            // Rust
            cargo: {
                install: 'cargo build',
                add: 'cargo add',
                remove: 'cargo remove',
                update: 'cargo update',
                list: 'cargo tree',
                outdated: 'cargo outdated',
                audit: 'cargo audit',
                clean: 'cargo clean',
                init: 'cargo init'
            },
            
            // Go
            go: {
                install: 'go mod download',
                add: 'go get',
                remove: 'go mod edit -droprequire',
                update: 'go get -u',
                list: 'go list -m all',
                outdated: 'go list -u -m all',
                audit: 'go mod verify',
                clean: 'go clean -modcache',
                init: 'go mod init'
            },
            
            // PHP
            composer: {
                install: 'composer install',
                add: 'composer require',
                remove: 'composer remove',
                update: 'composer update',
                list: 'composer show',
                outdated: 'composer outdated',
                audit: 'composer audit',
                clean: 'composer clear-cache',
                init: 'composer init'
            },
            
            // Ruby
            gem: {
                install: 'gem install',
                add: 'gem install',
                remove: 'gem uninstall',
                update: 'gem update',
                list: 'gem list',
                outdated: 'gem outdated',
                audit: 'gem audit',
                clean: 'gem cleanup',
                init: 'gem init'
            },
            bundler: {
                install: 'bundle install',
                add: 'bundle add',
                remove: 'bundle remove',
                update: 'bundle update',
                list: 'bundle list',
                outdated: 'bundle outdated',
                audit: 'bundle audit',
                clean: 'bundle clean',
                init: 'bundle init'
            },
            
            // Java
            maven: {
                install: 'mvn install',
                add: 'mvn dependency:get',
                remove: 'mvn dependency:purge-local-repository',
                update: 'mvn versions:use-latest-versions',
                list: 'mvn dependency:tree',
                outdated: 'mvn versions:display-dependency-updates',
                audit: 'mvn dependency:analyze',
                clean: 'mvn clean',
                init: 'mvn archetype:generate'
            },
            gradle: {
                install: 'gradle build',
                add: 'gradle dependencies --write-locks',
                remove: 'gradle dependencies',
                update: 'gradle dependencyUpdates',
                list: 'gradle dependencies',
                outdated: 'gradle dependencyUpdates',
                audit: 'gradle dependencyInsight',
                clean: 'gradle clean',
                init: 'gradle init'
            },
            
            // .NET
            dotnet: {
                install: 'dotnet restore',
                add: 'dotnet add package',
                remove: 'dotnet remove package',
                update: 'dotnet add package --version',
                list: 'dotnet list package',
                outdated: 'dotnet list package --outdated',
                audit: 'dotnet list package --vulnerable',
                clean: 'dotnet clean',
                init: 'dotnet new'
            },
            
            // Swift
            swift: {
                install: 'swift package resolve',
                add: 'swift package add-dependency',
                remove: 'swift package remove-dependency',
                update: 'swift package update',
                list: 'swift package show-dependencies',
                outdated: 'swift package show-dependencies --format json',
                audit: 'swift package audit',
                clean: 'swift package clean',
                init: 'swift package init'
            },
            
            // Dart
            pub: {
                install: 'dart pub get',
                add: 'dart pub add',
                remove: 'dart pub remove',
                update: 'dart pub upgrade',
                list: 'dart pub deps',
                outdated: 'dart pub outdated',
                audit: 'dart pub audit',
                clean: 'dart pub cache clean',
                init: 'dart create'
            }
        };
    }
    
    async detectLanguagesAndManagers() {
        const detected = new Map();
        
        try {
            // Scan project directory for language-specific files
            const files = await this.scanDirectory(this.projectRoot);
            
            for (const [language, config] of Object.entries(this.supportedLanguages)) {
                const foundFiles = files.filter(file => 
                    config.files.some(pattern => 
                        this.matchesPattern(file, pattern)
                    )
                );
                
                if (foundFiles.length > 0) {
                    const managers = await this.detectManagersForLanguage(language, foundFiles);
                    if (managers.length > 0) {
                        detected.set(language, {
                            managers,
                            files: foundFiles,
                            primary: managers[0] // First detected is primary
                        });
                    }
                }
            }
            
            this.detectedManagers = detected;
            this.logger.info('Detected languages and package managers:', 
                Object.fromEntries(detected));
            
            return detected;
            
        } catch (error) {
            this.logger.error('Failed to detect languages and managers:', error);
            throw error;
        }
    }
    
    async scanDirectory(dir, maxDepth = 3, currentDepth = 0) {
        const files = [];
        
        if (currentDepth >= maxDepth) return files;
        
        try {
            const entries = await fs.readdir(dir, { withFileTypes: true });
            
            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);
                const relativePath = path.relative(this.projectRoot, fullPath);
                
                // Skip common ignore patterns
                if (this.shouldIgnore(entry.name)) continue;
                
                if (entry.isDirectory()) {
                    const subFiles = await this.scanDirectory(fullPath, maxDepth, currentDepth + 1);
                    files.push(...subFiles);
                } else {
                    files.push(relativePath);
                }
            }
        } catch (error) {
            this.logger.warn(`Failed to scan directory ${dir}:`, error.message);
        }
        
        return files;
    }
    
    shouldIgnore(name) {
        const ignorePatterns = [
            'node_modules', '.git', '.svn', '.hg',
            'target', 'build', 'dist', 'out',
            '__pycache__', '.pytest_cache',
            '.venv', 'venv', 'env',
            '.idea', '.vscode',
            'coverage', '.nyc_output'
        ];
        
        return ignorePatterns.includes(name) || name.startsWith('.');
    }
    
    matchesPattern(filename, pattern) {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(filename);
        }
        return filename === pattern || filename.endsWith(pattern);
    }
    
    async detectManagersForLanguage(language, files) {
        const config = this.supportedLanguages[language];
        const detectedManagers = [];
        
        for (const manager of config.managers) {
            const isAvailable = await this.isManagerAvailable(manager);
            if (isAvailable) {
                // Check if manager-specific files exist
                const managerFiles = this.getManagerFiles(manager);
                const hasManagerFiles = files.some(file => 
                    managerFiles.some(pattern => this.matchesPattern(file, pattern))
                );
                
                if (hasManagerFiles || manager === config.managers[0]) {
                    detectedManagers.push(manager);
                }
            }
        }
        
        return detectedManagers;
    }
    
    getManagerFiles(manager) {
        const managerFileMap = {
            npm: ['package.json', 'package-lock.json'],
            yarn: ['package.json', 'yarn.lock'],
            pnpm: ['package.json', 'pnpm-lock.yaml'],
            bun: ['package.json', 'bun.lockb'],
            pip: ['requirements.txt', 'setup.py'],
            poetry: ['pyproject.toml', 'poetry.lock'],
            conda: ['environment.yml', 'environment.yaml'],
            pipenv: ['Pipfile', 'Pipfile.lock'],
            cargo: ['Cargo.toml', 'Cargo.lock'],
            go: ['go.mod', 'go.sum'],
            composer: ['composer.json', 'composer.lock'],
            gem: ['*.gemspec'],
            bundler: ['Gemfile', 'Gemfile.lock'],
            maven: ['pom.xml'],
            gradle: ['build.gradle', 'build.gradle.kts'],
            dotnet: ['*.csproj', '*.sln'],
            swift: ['Package.swift'],
            pub: ['pubspec.yaml', 'pubspec.lock']
        };
        
        return managerFileMap[manager] || [];
    }
    
    async isManagerAvailable(manager) {
        try {
            execSync(`${manager} --version`, { 
                stdio: 'ignore',
                timeout: 5000
            });
            return true;
        } catch (error) {
            return false;
        }
    }
    
    async executeCommand(language, action, packages = [], options = {}) {
        const languageConfig = this.detectedManagers.get(language);
        if (!languageConfig) {
            throw new Error(`Language ${language} not detected in project`);
        }
        
        const manager = options.manager || languageConfig.primary;
        const commands = this.commands[manager];
        
        if (!commands || !commands[action]) {
            throw new Error(`Action ${action} not supported for ${manager}`);
        }
        
        let command = commands[action];
        
        // Add packages to command if provided
        if (packages.length > 0 && ['add', 'remove', 'update'].includes(action)) {
            command += ' ' + packages.join(' ');
        }
        
        // Add additional options
        if (options.dev && ['add'].includes(action)) {
            if (manager === 'npm' || manager === 'yarn' || manager === 'pnpm') {
                command += ' --save-dev';
            } else if (manager === 'poetry') {
                command += ' --group dev';
            }
        }
        
        if (options.global && ['add', 'install'].includes(action)) {
            if (['npm', 'yarn', 'pnpm'].includes(manager)) {
                command += ' --global';
            } else if (manager === 'pip') {
                command += ' --user';
            }
        }
        
        this.logger.info(`Executing: ${command}`);
        
        return new Promise((resolve, reject) => {
            const [cmd, ...args] = command.split(' ');
            const process = spawn(cmd, args, {
                cwd: this.projectRoot,
                stdio: options.silent ? 'pipe' : 'inherit',
                shell: true
            });
            
            let output = '';
            let error = '';
            
            if (options.silent) {
                process.stdout?.on('data', (data) => {
                    output += data.toString();
                });
                
                process.stderr?.on('data', (data) => {
                    error += data.toString();
                });
            }
            
            process.on('close', (code) => {
                if (code === 0) {
                    resolve({
                        success: true,
                        output,
                        command
                    });
                } else {
                    reject(new Error(`Command failed with code ${code}: ${error || output}`));
                }
            });
            
            process.on('error', (err) => {
                reject(err);
            });
        });
    }
    
    async installDependencies(language, options = {}) {
        return this.executeCommand(language, 'install', [], options);
    }
    
    async addPackage(language, packages, options = {}) {
        if (typeof packages === 'string') {
            packages = [packages];
        }
        return this.executeCommand(language, 'add', packages, options);
    }
    
    async removePackage(language, packages, options = {}) {
        if (typeof packages === 'string') {
            packages = [packages];
        }
        return this.executeCommand(language, 'remove', packages, options);
    }
    
    async updatePackages(language, packages = [], options = {}) {
        return this.executeCommand(language, 'update', packages, options);
    }
    
    async listPackages(language, options = {}) {
        return this.executeCommand(language, 'list', [], { ...options, silent: true });
    }
    
    async auditPackages(language, options = {}) {
        return this.executeCommand(language, 'audit', [], { ...options, silent: true });
    }
    
    async getOutdatedPackages(language, options = {}) {
        return this.executeCommand(language, 'outdated', [], { ...options, silent: true });
    }
    
    async cleanCache(language, options = {}) {
        return this.executeCommand(language, 'clean', [], options);
    }
    
    async initializeProject(language, options = {}) {
        return this.executeCommand(language, 'init', [], options);
    }
    
    getDetectedLanguages() {
        return Array.from(this.detectedManagers.keys());
    }
    
    getManagersForLanguage(language) {
        const config = this.detectedManagers.get(language);
        return config ? config.managers : [];
    }
    
    getPrimaryManager(language) {
        const config = this.detectedManagers.get(language);
        return config ? config.primary : null;
    }
    
    getSupportedLanguages() {
        return Object.keys(this.supportedLanguages);
    }
    
    getSupportedManagers() {
        return Object.keys(this.commands);
    }
}
