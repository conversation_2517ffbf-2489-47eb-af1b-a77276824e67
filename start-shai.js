#!/usr/bin/env node

/**
 * SHAI AI Assistant - Production Startup Script
 * Ensures proper initialization and handles all startup scenarios
 */

import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';
import chalk from 'chalk'; 

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

console.log(chalk.cyan('🤖 SHAI AI Assistant - Advanced AI Coding Assistant'));
console.log(chalk.gray('Powered by Gemini 2.0 Flash with Multi-Agent Architecture\n'));

async function startShai() {
    try {
        // Check environment
        console.log(chalk.blue('🔍 Checking environment...'));
        
        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your_gemini_api_key_here') {
            console.log(chalk.yellow('⚠️  Warning: Gemini API key not configured'));
            console.log(chalk.gray('   Please add your API key to the .env file'));
            console.log(chalk.gray('   Get your key from: https://makersuite.google.com/app/apikey\n'));
        } else {
            console.log(chalk.green('✅ Gemini API key configured'));
        }
        
        // Check Node.js version
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 18) {
            console.log(chalk.red(`❌ Node.js ${nodeVersion} is not supported`));
            console.log(chalk.gray('   Please upgrade to Node.js 18.0.0 or higher'));
            process.exit(1);
        } else {
            console.log(chalk.green(`✅ Node.js ${nodeVersion} is supported`));
        }
        
        console.log(chalk.blue('\n🚀 Starting SHAI AI Assistant...\n'));
        
        // Import and start the main application
        const { ShaiAssistant } = await import('./src/index.js');

        // Create and start the application
        const app = new ShaiAssistant();
        await app.start();
        
    } catch (error) {
        console.error(chalk.red('\n❌ Failed to start SHAI AI Assistant:'));
        console.error(chalk.red(error.message));
        
        if (error.message.includes('Cannot resolve module')) {
            console.log(chalk.yellow('\n💡 This appears to be a missing dependency.'));
            console.log(chalk.gray('   Please run: bun install'));
        } else if (error.message.includes('GEMINI_API_KEY')) {
            console.log(chalk.yellow('\n💡 Please configure your Gemini API key.'));
            console.log(chalk.gray('   1. Copy .env.example to .env'));
            console.log(chalk.gray('   2. Add your API key from https://makersuite.google.com/app/apikey'));
        } else if (error.message.includes('ENOENT')) {
            console.log(chalk.yellow('\n💡 Some files may be missing.'));
            console.log(chalk.gray('   Please ensure all project files are present.'));
        }
        
        console.log(chalk.gray('\n🔧 Troubleshooting:'));
        console.log(chalk.gray('   1. Run: bun install'));
        console.log(chalk.gray('   2. Check your .env file'));
        console.log(chalk.gray('   3. Run: node test-startup.js'));
        console.log(chalk.gray('   4. Check the logs for more details'));
        
        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', () => {
    console.log(chalk.yellow('\n\n🛑 Received SIGINT, shutting down gracefully...'));
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log(chalk.yellow('\n\n🛑 Received SIGTERM, shutting down gracefully...'));
    process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(chalk.red('\n❌ Uncaught Exception:'), error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk.red('\n❌ Unhandled Rejection:'), reason);
    process.exit(1);
});

// Start the application
startShai();
