/**
 * <PERSON><PERSON> - Main coordinator for all agents and tasks
 */

import { EventEmitter } from 'events';
import { GeminiClient } from './gemini-client.js';
import { ContextEngine } from './context-engine.js';
import { MemoryManager } from './memory-manager.js';
import { BaseAgent } from '../agents/base-agent.js';
import { CodeAnalysisAgent } from '../agents/code-analysis-agent.js';
import { CodeGenerationAgent } from '../agents/code-generation-agent.js';
import { TestingAgent } from '../agents/testing-agent.js';
import { DocumentationAgent } from '../agents/documentation-agent.js';
import { DebuggingAgent } from '../agents/debugging-agent.js';
import { SecurityAgent } from '../agents/security-agent.js';
import { BackendDevelopmentAgent } from '../agents/backend-development-agent.js';
import { FrontendDevelopmentAgent } from '../agents/frontend-development-agent.js';
import { UIUXDesignerAgent } from '../agents/uiux-designer-agent.js';
import { UniversalCoderAgent } from '../agents/universal-coder-agent.js';
import { DevOpsInfrastructureAgent } from '../agents/devops-infrastructure-agent.js';
import { WebSearchTool } from '../tools/web-search.js';
import { MultiLanguagePackageManager } from '../tools/multi-language-package-manager.js';
import { CodeQualityLinter } from '../tools/code-quality-linter.js';
import { v4 as uuidv4 } from 'uuid';

export class Orchestrator extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger.child('Orchestrator');

        // Core components
        this.geminiClient = null;
        this.contextEngine = null;
        this.memoryManager = null;

        // Tools
        this.webSearchTool = null;
        this.packageManager = null;
        this.codeQualityLinter = null;

        // Agents
        this.agents = new Map();
        this.taskQueue = [];
        this.activeTasks = new Map();
        this.taskHistory = [];

        this.isInitialized = false;
        this.isProcessingQueue = false;

        // Performance metrics
        this.metrics = {
            tasksProcessed: 0,
            averageTaskTime: 0,
            successRate: 0,
            agentUtilization: {},
            errors: 0
        };
    }
    
    async initialize() {
        try {
            this.logger.info('Initializing Orchestrator');
            
            // Initialize Gemini client
            this.geminiClient = new GeminiClient(this.config, this.logger);
            await this.geminiClient.initialize();
            
            // Initialize Context Engine
            this.contextEngine = new ContextEngine(this.config, this.logger);
            await this.contextEngine.initialize();

            // Initialize memory manager
            this.memoryManager = new MemoryManager(this.config, this.logger);
            await this.memoryManager.initialize();

            // Initialize tools
            await this.initializeTools();

            // Initialize specialized agents
            await this.initializeAgents();
            
            // Start task processing
            this.startTaskProcessing();
            
            this.isInitialized = true;
            this.logger.info('Orchestrator initialized successfully');
            
            return true;
        } catch (error) {
            this.logger.error('Failed to initialize Orchestrator:', error);
            throw error;
        }
    }

    async initializeTools() {
        try {
            this.logger.info('Initializing tools');

            // Initialize web search tool
            this.webSearchTool = new WebSearchTool(this.logger);

            // Initialize multi-language package manager
            this.packageManager = new MultiLanguagePackageManager(this.logger);
            await this.packageManager.detectLanguagesAndManagers();

            // Initialize code quality linter
            this.codeQualityLinter = new CodeQualityLinter(this.logger);

            this.logger.info('Tools initialized successfully');

        } catch (error) {
            this.logger.error('Failed to initialize tools:', error);
            throw error;
        }
    }

    async initializeAgents() {
        this.logger.info('Initializing specialized agents');
        
        const agentConfigs = [
            { name: 'CodeAnalysis', class: CodeAnalysisAgent, specialization: 'code-analysis' },
            { name: 'CodeGeneration', class: CodeGenerationAgent, specialization: 'code-generation' },
            { name: 'Testing', class: TestingAgent, specialization: 'testing' },
            { name: 'Documentation', class: DocumentationAgent, specialization: 'documentation' },
            { name: 'Debugging', class: DebuggingAgent, specialization: 'debugging' },
            { name: 'Security', class: SecurityAgent, specialization: 'security' },
            { name: 'BackendDevelopment', class: BackendDevelopmentAgent, specialization: 'backend-development' },
            { name: 'FrontendDevelopment', class: FrontendDevelopmentAgent, specialization: 'frontend-development' },
            { name: 'UIUXDesigner', class: UIUXDesignerAgent, specialization: 'uiux-designer' },
            { name: 'UniversalCoder', class: UniversalCoderAgent, specialization: 'universal-coder' },
            { name: 'DevOpsInfrastructure', class: DevOpsInfrastructureAgent, specialization: 'devops-infrastructure' }
        ];
        
        for (const agentConfig of agentConfigs) {
            try {
                const agent = new agentConfig.class(this, this.logger);
                
                await agent.initialize();
                this.agents.set(agentConfig.specialization, agent);
                
                // Set up agent event listeners
                this.setupAgentEventListeners(agent);
                
                this.logger.info(`Initialized ${agentConfig.name} agent`);
            } catch (error) {
                this.logger.error(`Failed to initialize ${agentConfig.name} agent:`, error);
                // Continue with other agents
            }
        }
        
        this.logger.info(`Initialized ${this.agents.size} specialized agents`);
    }
    
    setupAgentEventListeners(agent) {
        agent.on('taskStarted', (data) => {
            this.logger.debug(`Agent ${data.agentId} started task ${data.taskId}`);
            this.emit('agentTaskStarted', data);
        });
        
        agent.on('taskCompleted', (data) => {
            this.logger.debug(`Agent ${data.agentId} completed task ${data.taskId}`);
            this.handleTaskCompletion(data);
        });
        
        agent.on('taskFailed', (data) => {
            this.logger.warn(`Agent ${data.agentId} failed task ${data.taskId}: ${data.error}`);
            this.handleTaskFailure(data);
        });
    }
    
    startTaskProcessing() {
        if (this.isProcessingQueue) return;
        
        this.isProcessingQueue = true;
        this.processTaskQueue();
    }
    
    async processTaskQueue() {
        while (this.isProcessingQueue && this.taskQueue.length > 0) {
            try {
                const task = this.taskQueue.shift();
                await this.executeTask(task);
            } catch (error) {
                this.logger.error('Task processing error:', error);
            }
            
            // Small delay to prevent overwhelming
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        // Continue processing if there are more tasks
        if (this.taskQueue.length > 0) {
            setTimeout(() => this.processTaskQueue(), 100);
        }
    }
    
    async executeTask(task) {
        const taskId = task.id || uuidv4();
        const startTime = Date.now();
        
        try {
            this.logger.info(`Executing task: ${task.type}`, { taskId });
            
            // Determine the best agent for this task
            const agent = await this.selectAgent(task);
            if (!agent) {
                throw new Error(`No suitable agent found for task type: ${task.type}`);
            }
            
            // Add to active tasks
            this.activeTasks.set(taskId, {
                ...task,
                id: taskId,
                agent: agent.name,
                startTime
            });
            
            // Execute the task
            const result = await agent.executeTask(task);
            
            // Handle successful completion
            const duration = Date.now() - startTime;
            this.updateMetrics(true, duration, agent.specialization);
            
            const completedTask = {
                ...task,
                id: taskId,
                result,
                duration,
                agent: agent.name,
                timestamp: new Date(),
                success: true
            };
            
            this.taskHistory.push(completedTask);
            this.activeTasks.delete(taskId);
            
            this.logger.info(`Task completed successfully`, { taskId, duration, agent: agent.name });
            this.emit('taskCompleted', completedTask);
            
            return result;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            this.updateMetrics(false, duration);
            
            const failedTask = {
                ...task,
                id: taskId,
                error: error.message,
                duration,
                timestamp: new Date(),
                success: false
            };
            
            this.taskHistory.push(failedTask);
            this.activeTasks.delete(taskId);
            
            this.logger.error(`Task failed:`, error, { taskId, duration });
            this.emit('taskFailed', failedTask);
            
            throw error;
        }
    }
    
    async selectAgent(task) {
        // Simple agent selection based on task type
        const agentMapping = {
            'analyze-code': 'code-analysis',
            'generate-code': 'code-generation',
            'create-tests': 'testing',
            'write-documentation': 'documentation',
            'debug-code': 'debugging',
            'security-audit': 'security',
            'refactor-code': 'code-analysis',
            'explain-code': 'code-analysis',
            'optimize-code': 'code-analysis'
        };
        
        const specialization = agentMapping[task.type];
        if (specialization && this.agents.has(specialization)) {
            const agent = this.agents.get(specialization);
            
            // Check if agent is available
            if (agent.status === 'idle') {
                return agent;
            }
        }
        
        // Fallback: find any available agent that can handle the task
        for (const agent of this.agents.values()) {
            if (agent.status === 'idle' && agent.canHandleTask(task)) {
                return agent;
            }
        }
        
        // If no agent is available, wait and try again
        await new Promise(resolve => setTimeout(resolve, 1000));
        return this.selectAgent(task);
    }
    
    handleTaskCompletion(data) {
        this.emit('taskCompleted', data);
    }
    
    handleTaskFailure(data) {
        this.emit('taskFailed', data);
    }
    
    updateMetrics(success, duration, agentType = null) {
        this.metrics.tasksProcessed++;
        
        if (success) {
            // Update average task time
            const totalTime = this.metrics.averageTaskTime * (this.metrics.tasksProcessed - 1) + duration;
            this.metrics.averageTaskTime = totalTime / this.metrics.tasksProcessed;
            
            // Update success rate
            const successfulTasks = this.metrics.tasksProcessed - this.metrics.errors;
            this.metrics.successRate = (successfulTasks / this.metrics.tasksProcessed) * 100;
            
            // Update agent utilization
            if (agentType) {
                if (!this.metrics.agentUtilization[agentType]) {
                    this.metrics.agentUtilization[agentType] = 0;
                }
                this.metrics.agentUtilization[agentType]++;
            }
        } else {
            this.metrics.errors++;
            this.metrics.successRate = ((this.metrics.tasksProcessed - this.metrics.errors) / this.metrics.tasksProcessed) * 100;
        }
    }
    
    // Public API methods
    async processRequest(request) {
        if (!this.isInitialized) {
            throw new Error('Orchestrator not initialized');
        }
        
        // Parse and validate request
        const task = this.parseRequest(request);
        
        // Add to queue or execute immediately
        if (this.shouldExecuteImmediately(task)) {
            return await this.executeTask(task);
        } else {
            return this.queueTask(task);
        }
    }
    
    parseRequest(request) {
        // Convert natural language request to structured task
        const task = {
            id: uuidv4(),
            type: this.inferTaskType(request),
            description: request.description || request.query || request,
            query: request.query,
            files: request.files || [],
            code: request.code,
            language: request.language,
            requirements: request.requirements || [],
            priority: request.priority || 'normal',
            timestamp: new Date()
        };
        
        return task;
    }
    
    inferTaskType(request) {
        const text = (request.description || request.query || request).toLowerCase();
        
        if (text.includes('analyze') || text.includes('review')) return 'analyze-code';
        if (text.includes('generate') || text.includes('create') || text.includes('write')) {
            if (text.includes('test')) return 'create-tests';
            if (text.includes('doc')) return 'write-documentation';
            return 'generate-code';
        }
        if (text.includes('debug') || text.includes('fix') || text.includes('error')) return 'debug-code';
        if (text.includes('security') || text.includes('vulnerability')) return 'security-audit';
        if (text.includes('refactor') || text.includes('improve')) return 'refactor-code';
        if (text.includes('explain') || text.includes('understand')) return 'explain-code';
        if (text.includes('optimize') || text.includes('performance')) return 'optimize-code';
        
        return 'analyze-code'; // Default
    }
    
    shouldExecuteImmediately(task) {
        return task.priority === 'high' || this.activeTasks.size < this.config.performance.maxConcurrentRequests;
    }
    
    queueTask(task) {
        this.taskQueue.push(task);
        this.logger.info(`Task queued: ${task.type}`, { taskId: task.id, queueLength: this.taskQueue.length });
        
        return {
            taskId: task.id,
            status: 'queued',
            position: this.taskQueue.length,
            estimatedWaitTime: this.estimateWaitTime()
        };
    }
    
    estimateWaitTime() {
        const avgTaskTime = this.metrics.averageTaskTime || 30000; // 30 seconds default
        const queuePosition = this.taskQueue.length;
        const availableAgents = Array.from(this.agents.values()).filter(agent => agent.status === 'idle').length;
        
        if (availableAgents === 0) {
            return queuePosition * avgTaskTime;
        }
        
        return Math.ceil(queuePosition / availableAgents) * avgTaskTime;
    }
    
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            agents: Array.from(this.agents.values()).map(agent => agent.getStatus()),
            taskQueue: {
                length: this.taskQueue.length,
                activeTasks: this.activeTasks.size
            },
            metrics: this.metrics,
            contextEngine: this.contextEngine.getStats()
        };
    }
    
    getTaskHistory(limit = 50) {
        return this.taskHistory.slice(-limit);
    }
    
    async getAgent(specialization) {
        return this.agents.get(specialization);
    }
    
    async shutdown() {
        this.logger.info('Shutting down Orchestrator');
        
        // Stop task processing
        this.isProcessingQueue = false;
        
        // Wait for active tasks to complete
        while (this.activeTasks.size > 0) {
            this.logger.info(`Waiting for ${this.activeTasks.size} active tasks to complete`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // Shutdown all agents
        for (const agent of this.agents.values()) {
            await agent.shutdown();
        }
        
        // Shutdown core components
        if (this.contextEngine) {
            await this.contextEngine.shutdown();
        }
        
        if (this.geminiClient) {
            await this.geminiClient.shutdown();
        }
        
        this.isInitialized = false;
        this.logger.info('Orchestrator shutdown complete');
    }
}
